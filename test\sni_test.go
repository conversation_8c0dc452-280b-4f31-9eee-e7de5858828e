package test

import (
	"testing"
	"golang-rdp-proxy/internal/sni"
)

// TestSNIParser 测试SNI解析器
func TestSNIParser(t *testing.T) {
	// 模拟TLS ClientHello数据（包含SNI扩展）
	// 这是一个简化的测试数据，实际的ClientHello会更复杂
	clientHelloWithSNI := []byte{
		// TLS Record Header
		0x16,       // Content Type: Handshake
		0x03, 0x03, // Version: TLS 1.2
		0x00, 0x40, // Length: 64 bytes

		// Handshake Header
		0x01,             // Handshake Type: Client Hello
		0x00, 0x00, 0x3C, // Length: 60 bytes

		// Client Hello
		0x03, 0x03, // Version: TLS 1.2
		// Random (32 bytes)
		0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
		0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
		0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
		0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,

		0x00, // Session ID Length: 0

		// Cipher Suites
		0x00, 0x02, // Length: 2
		0x00, 0x35, // TLS_RSA_WITH_AES_256_CBC_SHA

		// Compression Methods
		0x01, // Length: 1
		0x00, // No compression

		// Extensions
		0x00, 0x17, // Extensions Length: 23 bytes

		// SNI Extension
		0x00, 0x00, // Extension Type: SNI
		0x00, 0x13, // Extension Length: 19 bytes
		0x00, 0x11, // Server Name List Length: 17 bytes
		0x00,       // Name Type: hostname
		0x00, 0x0E, // Name Length: 14 bytes
		// Hostname: "test.rdp.anan.cc"
		't', 'e', 's', 't', '.', 'r', 'd', 'p', '.', 'a', 'n', 'a', 'n', '.', 'c', 'c',
	}

	// 测试SNI解析
	sniValue, err := sni.ParseSNIFromTLS(clientHelloWithSNI)
	if err != nil {
		t.Fatalf("SNI解析失败: %v", err)
	}

	expectedSNI := "test.rdp.anan.cc"
	if sniValue != expectedSNI {
		t.Errorf("SNI解析结果不匹配，期望: %s, 实际: %s", expectedSNI, sniValue)
	}

	t.Logf("SNI解析成功: %s", sniValue)
}

// TestSNIParserNoSNI 测试无SNI的情况
func TestSNIParserNoSNI(t *testing.T) {
	// 模拟没有SNI扩展的ClientHello
	clientHelloNoSNI := []byte{
		// TLS Record Header
		0x16,       // Content Type: Handshake
		0x03, 0x03, // Version: TLS 1.2
		0x00, 0x2C, // Length: 44 bytes

		// Handshake Header
		0x01,             // Handshake Type: Client Hello
		0x00, 0x00, 0x28, // Length: 40 bytes

		// Client Hello
		0x03, 0x03, // Version: TLS 1.2
		// Random (32 bytes)
		0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
		0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
		0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
		0x18, 0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F,

		0x00, // Session ID Length: 0

		// Cipher Suites
		0x00, 0x02, // Length: 2
		0x00, 0x35, // TLS_RSA_WITH_AES_256_CBC_SHA

		// Compression Methods
		0x01, // Length: 1
		0x00, // No compression

		// No Extensions
		0x00, 0x00, // Extensions Length: 0
	}

	// 测试无SNI解析
	_, err := sni.ParseSNIFromTLS(clientHelloNoSNI)
	if err == nil {
		t.Error("期望SNI解析失败，但实际成功了")
	}

	t.Logf("无SNI测试通过，错误信息: %v", err)
}

// TestInvalidTLSData 测试无效TLS数据
func TestInvalidTLSData(t *testing.T) {
	// 测试数据太短
	shortData := []byte{0x16, 0x03}
	_, err := sni.ParseSNIFromTLS(shortData)
	if err == nil {
		t.Error("期望解析失败，但实际成功了")
	}

	// 测试非握手记录
	nonHandshakeData := []byte{
		0x17,       // Content Type: Application Data
		0x03, 0x03, // Version
		0x00, 0x05, // Length
		0x01, 0x02, 0x03, 0x04, 0x05, // Data
	}
	_, err = sni.ParseSNIFromTLS(nonHandshakeData)
	if err == nil {
		t.Error("期望解析失败，但实际成功了")
	}

	t.Logf("无效数据测试通过")
}
