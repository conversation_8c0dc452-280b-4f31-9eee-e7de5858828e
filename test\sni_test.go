package test

import (
	"golang-rdp-proxy/internal/config"
	"testing"
)

// TestConfig 测试配置功能
func TestConfig(t *testing.T) {
	cfg := config.NewConfig()

	// 测试默认配置
	if cfg.ListenPort != 3389 {
		t.<PERSON><PERSON><PERSON>("期望监听端口3389，实际: %d", cfg.ListenPort)
	}

	// 测试SNI路由
	backendAddr, err := cfg.GetBackendAddr("20201.rdp.anan.cc")
	if err != nil {
		t.Errorf("获取后端地址失败: %v", err)
	}

	expectedAddr := "127.0.0.1:20201"
	if backendAddr != expectedAddr {
		t.<PERSON><PERSON>("期望后端地址: %s, 实际: %s", expectedAddr, backendAddr)
	}

	// 测试无效SNI
	_, err = cfg.GetBackendAddr("invalid.domain.com")
	if err == nil {
		t.Error("期望无效SNI返回错误，但实际成功了")
	}

	t.Logf("配置测试通过")
}

// TestSNIValidation 测试SNI验证功能
func TestSNIValidation(t *testing.T) {
	cfg := config.NewConfig()

	// 测试有效SNI
	if !cfg.IsValidSNI("20201.rdp.anan.cc") {
		t.Error("期望SNI有效，但验证失败")
	}

	if !cfg.IsValidSNI("127.rdp.anan.cc") {
		t.Error("期望SNI有效，但验证失败")
	}

	// 测试无效SNI
	if cfg.IsValidSNI("invalid.domain.com") {
		t.Error("期望SNI无效，但验证通过")
	}

	if cfg.IsValidSNI("") {
		t.Error("期望空SNI无效，但验证通过")
	}

	t.Logf("SNI验证测试通过")
}
