package rdp

import (
	"encoding/binary"
	"fmt"
)

// RDP协议常量
const (
	// X.224 TPDU类型
	X224_TPDU_CONNECTION_REQUEST = 0xE0
	X224_TPDU_CONNECTION_CONFIRM = 0xD0
	X224_TPDU_DISCONNECT_REQUEST = 0x80
	X224_TPDU_DATA_TRANSFER      = 0xF0

	// RDP协议类型
	RDP_NEG_REQ_TYPE     = 0x01
	RDP_NEG_RSP_TYPE     = 0x02
	RDP_NEG_FAILURE_TYPE = 0x03

	// RDP协议标志
	PROTOCOL_RDP       = 0x00000000
	PROTOCOL_SSL       = 0x00000001
	PROTOCOL_HYBRID    = 0x00000002
	PROTOCOL_RDSTLS    = 0x00000004
	PROTOCOL_HYBRID_EX = 0x00000008

	// MCS PDU类型
	MCS_TYPE_CONNECT_INITIAL  = 0x65
	MCS_TYPE_CONNECT_RESPONSE = 0x66

	// GCC PDU类型
	GCC_CREATE_REQUEST  = 0x00
	GCC_CREATE_RESPONSE = 0x14
)

// X224ConnectionRequest X.224连接请求
type X224ConnectionRequest struct {
	Length     uint8
	Code       uint8
	DstRef     uint16
	SrcRef     uint16
	Class      uint8
	Cookie     string
	RDPNegData *RDPNegotiationRequest
}

// X224ConnectionConfirm X.224连接确认
type X224ConnectionConfirm struct {
	Length     uint8
	Code       uint8
	DstRef     uint16
	SrcRef     uint16
	Class      uint8
	RDPNegData *RDPNegotiationResponse
}

// RDPNegotiationRequest RDP协商请求
type RDPNegotiationRequest struct {
	Type               uint8
	Flags              uint8
	Length             uint16
	RequestedProtocols uint32
}

// RDPNegotiationResponse RDP协商响应
type RDPNegotiationResponse struct {
	Type             uint8
	Flags            uint8
	Length           uint16
	SelectedProtocol uint32
}

// MCSConnectInitial MCS连接初始化
type MCSConnectInitial struct {
	CallingDomainSelector []byte
	CalledDomainSelector  []byte
	UpwardFlag            bool
	TargetParameters      []byte
	MinimumParameters     []byte
	MaximumParameters     []byte
	UserData              []byte
}

// MCSConnectResponse MCS连接响应
type MCSConnectResponse struct {
	Result           uint8
	CalledConnectId  uint32
	DomainParameters []byte
	UserData         []byte
}

// ParseX224ConnectionRequest 解析X.224连接请求
func ParseX224ConnectionRequest(data []byte) (*X224ConnectionRequest, error) {
	if len(data) < 7 {
		return nil, fmt.Errorf("X.224 CR too short")
	}

	req := &X224ConnectionRequest{
		Length: data[0],
		Code:   data[1],
		DstRef: binary.BigEndian.Uint16(data[2:4]),
		SrcRef: binary.BigEndian.Uint16(data[4:6]),
		Class:  data[6],
	}

	offset := 7

	// 解析Cookie和RDP协商数据
	if len(data) > offset {
		remaining := data[offset:]

		// 查找Cookie
		cookieEnd := -1
		for i, b := range remaining {
			if b == 0x0D && i+1 < len(remaining) && remaining[i+1] == 0x0A {
				cookieEnd = i
				break
			}
		}

		if cookieEnd > 0 {
			req.Cookie = string(remaining[:cookieEnd])
			offset += cookieEnd + 2 // 跳过\r\n
		}

		// 解析RDP协商数据
		if len(data) > offset && len(data) >= offset+8 {
			rdpNegData := &RDPNegotiationRequest{
				Type:               data[offset],
				Flags:              data[offset+1],
				Length:             binary.LittleEndian.Uint16(data[offset+2 : offset+4]),
				RequestedProtocols: binary.LittleEndian.Uint32(data[offset+4 : offset+8]),
			}
			req.RDPNegData = rdpNegData
		}
	}

	return req, nil
}

// BuildX224ConnectionConfirm 构建X.224连接确认
func BuildX224ConnectionConfirm(srcRef, dstRef uint16, selectedProtocol uint32) []byte {
	// 基础X.224 CC结构
	cc := []byte{
		0x0B,       // Length
		0xD0,       // Code (Connection Confirm)
		0x00, 0x00, // DstRef (will be filled)
		0x00, 0x00, // SrcRef (will be filled)
		0x00, // Class
	}

	binary.BigEndian.PutUint16(cc[2:4], dstRef)
	binary.BigEndian.PutUint16(cc[4:6], srcRef)

	// 添加RDP协商响应
	rdpNegResp := []byte{
		RDP_NEG_RSP_TYPE, // Type
		0x00,             // Flags
		0x08, 0x00,       // Length (8 bytes)
		0x00, 0x00, 0x00, 0x00, // Selected Protocol (will be filled)
	}

	binary.LittleEndian.PutUint32(rdpNegResp[4:8], selectedProtocol)

	// 更新长度
	cc[0] = uint8(len(cc) - 1 + len(rdpNegResp))

	return append(cc, rdpNegResp...)
}

// ParseMCSConnectInitial 解析MCS连接初始化
func ParseMCSConnectInitial(data []byte) (*MCSConnectInitial, error) {
	if len(data) < 10 {
		return nil, fmt.Errorf("MCS Connect-Initial too short")
	}

	// 简化的MCS解析，实际实现需要完整的BER/DER解析
	mci := &MCSConnectInitial{}

	// 这里需要实现完整的BER解析
	// 为了简化，我们假设已知的结构
	offset := 0

	// 跳过BER标签和长度
	if data[offset] == 0x65 { // Connect-Initial tag
		offset++
		// 跳过长度字段（可能是多字节）
		if data[offset]&0x80 != 0 {
			lengthBytes := int(data[offset] & 0x7F)
			offset += 1 + lengthBytes
		} else {
			offset++
		}
	}

	// 提取用户数据（简化处理）
	if len(data) > offset+100 {
		mci.UserData = data[offset+50:] // 简化的用户数据提取
	}

	return mci, nil
}

// BuildMCSConnectResponse 构建MCS连接响应
func BuildMCSConnectResponse() []byte {
	// 更准确的MCS Connect-Response，基于真实RDP服务器的响应
	response := []byte{
		// MCS Connect-Response
		0x66, 0x82, 0x01, 0x94, // Connect-Response tag and length (404 bytes)

		// Result
		0x0a, 0x01, 0x00, // rt-successful (0)

		// Called connect id
		0x02, 0x01, 0x0c, // connect id = 12

		// Domain parameters
		0x30, 0x1a, // SEQUENCE, length 26
		0x02, 0x01, 0x22, // maxChannelIds = 34
		0x02, 0x01, 0x20, // maxUserIds = 32
		0x02, 0x01, 0x00, // maxTokenIds = 0
		0x02, 0x01, 0x01, // numPriorities = 1
		0x02, 0x01, 0x00, // minThroughput = 0
		0x02, 0x01, 0x01, // maxHeight = 1
		0x02, 0x02, 0xff, 0xff, // maxMCSPDUsize = 65535
		0x02, 0x01, 0x02, // protocolVersion = 2

		// User data
		0x04, 0x82, 0x01, 0x68, // OCTET STRING, length 360
	}

	// GCC Conference Create Response
	gccResponse := []byte{
		// GCC Create Response
		0x00, 0x14, // T124Identifier = 20
		0x00, 0x01, // connectPDU = 1
		0x00, 0x00, // result = 0 (success)
		0x00, 0x00, // calledConnectId = 0

		// Server data blocks
		// SC_CORE (Server Core Data)
		0x02, 0x0c, 0x00, 0x00, // header: type=SC_CORE, length=12
		0x04, 0x00, 0x08, 0x00, // version = 0x00080004 (RDP 5.0)
		0x00, 0x00, 0x00, 0x00, // clientRequestedProtocols = 0

		// SC_SECURITY (Server Security Data)
		0x02, 0x0c, 0x00, 0x00, // header: type=SC_SECURITY, length=12
		0x01, 0x00, 0x00, 0x00, // encryptionMethod = 1 (40-bit)
		0x00, 0x00, 0x00, 0x00, // encryptionLevel = 0 (none)

		// SC_NET (Server Network Data)
		0x03, 0x0c, 0x00, 0x00, // header: type=SC_NET, length=12
		0x01, 0x00, // MCSChannelId = 1
		0x02, 0x00, // channelCount = 2
		0x00, 0x00, 0x00, 0x00, // channelIdArray (placeholder)
	}

	// 填充剩余空间以达到预期长度
	padding := make([]byte, 360-len(gccResponse))
	gccResponse = append(gccResponse, padding...)

	response = append(response, gccResponse...)

	return response
}

// BuildGCCConferenceCreateResponse 构建GCC会议创建响应
func BuildGCCConferenceCreateResponse() []byte {
	// 简化的GCC Conference Create Response
	response := []byte{
		0x00, 0x14, // GCC Create Response
		0x00, 0x01, // Node ID
		0x00, 0x00, // Tag
		0x00, 0x00, // Result
		// 服务器数据块
		0x02, 0x0c, 0x00, 0x00, // SC_CORE
		0x00, 0x00, 0x00, 0x00, // Version
		0x00, 0x08, 0x00, 0x00, // Client requested protocols
		0x00, 0x00, 0x00, 0x00, // Early capability flags
	}

	return response
}

// IsRDPData 检查是否为RDP数据
func IsRDPData(data []byte) bool {
	if len(data) < 4 {
		return false
	}

	// 检查TPKT头
	if data[0] == 0x03 && data[1] == 0x00 {
		return true
	}

	// 检查Fast-Path
	if (data[0] & 0x80) != 0 {
		return true
	}

	return false
}

// WrapTPKT 用TPKT包装数据
func WrapTPKT(data []byte) []byte {
	tpkt := make([]byte, 4+len(data))
	tpkt[0] = 0x03                                             // Version
	tpkt[1] = 0x00                                             // Reserved
	binary.BigEndian.PutUint16(tpkt[2:4], uint16(4+len(data))) // Length
	copy(tpkt[4:], data)
	return tpkt
}
