package proxy

import (
	"crypto/tls"
	"fmt"
	"io"
	"log"
	"net"
	"strings"
	"sync"
	"time"

	"golang-rdp-proxy/internal/config"
	tlsmgr "golang-rdp-proxy/internal/tls"
)

// Handler 代理处理器
type Handler struct {
	config     *config.Config
	tlsManager *tlsmgr.Manager
}

// NewHandler 创建代理处理器
func NewHandler(cfg *config.Config, tlsMgr *tlsmgr.Manager) *Handler {
	return &Handler{
		config:     cfg,
		tlsManager: tlsMgr,
	}
}

// HandleConnection 处理客户端连接
func (h *Handler) HandleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	clientAddr := clientConn.RemoteAddr().String()
	log.Printf("新客户端连接: %s", clientAddr)

	// 第一步：处理X.224连接请求
	if err := h.handleX224ConnectionRequest(clientConn); err != nil {
		log.Printf("客户端 %s X.224协商失败: %v", clientAddr, err)
		return
	}

	// 第二步：升级为TLS连接并获取SNI
	clientTLSConn, sniValue, err := h.upgradeToTLS(clientConn)
	if err != nil {
		log.Printf("客户端 %s TLS升级失败: %v", clientAddr, err)
		return
	}
	defer clientTLSConn.Close()

	log.Printf("客户端 %s TLS握手成功，SNI: %s", clientAddr, sniValue)

	// 第三步：验证SNI并获取后端地址
	if !h.config.IsValidSNI(sniValue) {
		log.Printf("客户端 %s SNI无效或不在允许列表中: %s", clientAddr, sniValue)
		return
	}

	backendAddr, err := h.config.GetBackendAddr(sniValue)
	if err != nil {
		log.Printf("客户端 %s 获取后端地址失败: %v", clientAddr, err)
		return
	}

	log.Printf("客户端 %s 路由到后端: %s", clientAddr, backendAddr)

	// 第四步：建立到后端的连接
	backendTLSConn, err := h.connectToBackend(backendAddr)
	if err != nil {
		log.Printf("连接后端 %s 失败: %v", backendAddr, err)
		return
	}
	defer backendTLSConn.Close()

	log.Printf("后端连接 %s 建立成功", backendAddr)

	// 第五步：开始双向数据转发
	h.relayData(clientTLSConn, backendTLSConn, clientAddr, backendAddr)
}

// handleX224ConnectionRequest 处理X.224连接请求
func (h *Handler) handleX224ConnectionRequest(conn net.Conn) error {
	// 读取TPKT头
	header := make([]byte, 4)
	if _, err := io.ReadFull(conn, header); err != nil {
		return fmt.Errorf("读取TPKT头失败: %v", err)
	}

	if header[0] != 0x03 {
		return fmt.Errorf("无效的TPKT版本: %d", header[0])
	}

	totalLen := int(header[2])<<8 | int(header[3])
	if totalLen < 4 {
		return fmt.Errorf("无效的TPKT长度: %d", totalLen)
	}

	// 读取X.224数据
	bodyLen := totalLen - 4
	body := make([]byte, bodyLen)
	if _, err := io.ReadFull(conn, body); err != nil {
		return fmt.Errorf("读取X.224数据失败: %v", err)
	}

	log.Printf("收到X.224连接请求，长度: %d", totalLen)

	// 发送X.224连接确认（选择TLS协议）
	if err := h.sendX224ConnectionConfirm(conn); err != nil {
		return fmt.Errorf("发送X.224连接确认失败: %v", err)
	}

	log.Printf("发送X.224连接确认，选择TLS协议")
	return nil
}

// sendX224ConnectionConfirm 发送X.224连接确认
func (h *Handler) sendX224ConnectionConfirm(conn net.Conn) error {
	// X.224 Connection Confirm
	x224CC := []byte{
		0x05,       // Length
		0xD0,       // Connection Confirm
		0x00, 0x00, // DST-REF
		0x00, 0x00, // SRC-REF
		0x00, // Class
	}

	// RDP Negotiation Response (选择TLS)
	rdpNegResp := []byte{
		0x02,       // Type: RDP_NEG_RSP
		0x00,       // Flags
		0x08, 0x00, // Length (8 bytes)
		0x01, 0x00, 0x00, 0x00, // Selected Protocol: TLS (0x00000001)
	}

	// 计算总长度
	totalLen := 4 + len(x224CC) + len(rdpNegResp)
	tpkt := []byte{
		0x03, 0x00, // TPKT Header
		byte(totalLen >> 8), byte(totalLen & 0xff),
	}

	// 组装完整数据包
	packet := append(tpkt, x224CC...)
	packet = append(packet, rdpNegResp...)

	_, err := conn.Write(packet)
	return err
}

// upgradeToTLS 升级连接为TLS并获取SNI
func (h *Handler) upgradeToTLS(conn net.Conn) (*tls.Conn, string, error) {
	// 创建TLS配置
	tlsConfig := h.tlsManager.CreateTLSConfig("")

	// 升级为TLS服务器连接
	tlsConn := tls.Server(conn, tlsConfig)

	// 执行TLS握手
	if err := tlsConn.Handshake(); err != nil {
		return nil, "", fmt.Errorf("TLS握手失败: %v", err)
	}

	// 从连接状态获取SNI
	sni := tlsConn.ConnectionState().ServerName
	if sni == "" {
		return nil, "", fmt.Errorf("客户端未提供SNI")
	}

	sni = strings.ToLower(strings.TrimSpace(sni))
	log.Printf("从TLS连接状态获取SNI: %s", sni)

	return tlsConn, sni, nil
}

// connectToBackend 连接到后端RDP服务器
func (h *Handler) connectToBackend(backendAddr string) (*tls.Conn, error) {
	// 连接到后端服务器
	conn, err := net.Dial("tcp", backendAddr)
	if err != nil {
		return nil, fmt.Errorf("连接后端服务器失败: %v", err)
	}

	log.Printf("已连接到后端: %s", backendAddr)

	// 发送X.224连接请求
	if err := h.sendX224ConnectionRequest(conn); err != nil {
		conn.Close()
		return nil, fmt.Errorf("发送X.224连接请求失败: %v", err)
	}

	// 读取X.224连接确认
	if err := h.readX224ConnectionConfirm(conn); err != nil {
		conn.Close()
		return nil, fmt.Errorf("读取X.224连接确认失败: %v", err)
	}

	log.Printf("后端X.224协商完成")

	// 升级为TLS客户端连接
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 简化处理，跳过证书验证
		MinVersion:         tls.VersionTLS12,
		MaxVersion:         tls.VersionTLS13,
	}

	tlsConn := tls.Client(conn, tlsConfig)
	if err := tlsConn.Handshake(); err != nil {
		conn.Close()
		return nil, fmt.Errorf("后端TLS握手失败: %v", err)
	}

	log.Printf("后端TLS握手成功")
	return tlsConn, nil
}

// sendX224ConnectionRequest 发送X.224连接请求到后端
func (h *Handler) sendX224ConnectionRequest(conn net.Conn) error {
	// X.224 Connection Request
	x224CR := []byte{
		// TPKT Header (4 bytes)
		0x03, 0x00,
		0x00, 0x13, // Length: 19 bytes

		// X.224 Connection Request (7 bytes)
		0x0E,       // Length
		0xE0,       // CR - Connection Request
		0x00, 0x00, // DST-REF
		0x00, 0x00, // SRC-REF
		0x00, // CLASS

		// RDP Negotiation Request (8 bytes)
		0x01,       // Type: RDP_NEG_REQ
		0x00,       // Flags
		0x08, 0x00, // Length (8 bytes)
		0x01, 0x00, 0x00, 0x00, // requestedProtocols: TLS (0x00000001)
	}

	_, err := conn.Write(x224CR)
	return err
}

// readX224ConnectionConfirm 读取X.224连接确认
func (h *Handler) readX224ConnectionConfirm(conn net.Conn) error {
	// 读取TPKT头
	header := make([]byte, 4)
	if _, err := io.ReadFull(conn, header); err != nil {
		return err
	}

	if header[0] != 0x03 {
		return fmt.Errorf("后端返回无效的TPKT版本")
	}

	totalLen := int(header[2])<<8 | int(header[3])
	if totalLen < 4 {
		return fmt.Errorf("后端返回无效的TPKT长度")
	}

	// 读取剩余数据
	rest := make([]byte, totalLen-4)
	if _, err := io.ReadFull(conn, rest); err != nil {
		return err
	}

	log.Printf("收到后端X.224连接确认")
	return nil
}

// relayData 双向数据转发
func (h *Handler) relayData(clientConn, backendConn net.Conn, clientAddr, backendAddr string) {
	log.Printf("开始数据转发: %s <-> %s", clientAddr, backendAddr)

	var wg sync.WaitGroup
	wg.Add(2)

	// 客户端到后端
	go func() {
		defer wg.Done()
		defer backendConn.Close()

		written, err := io.Copy(backendConn, clientConn)
		if err != nil {
			log.Printf("客户端->后端数据转发错误: %v", err)
		} else {
			log.Printf("客户端->后端数据转发完成，传输字节: %d", written)
		}
	}()

	// 后端到客户端
	go func() {
		defer wg.Done()
		defer clientConn.Close()

		written, err := io.Copy(clientConn, backendConn)
		if err != nil {
			log.Printf("后端->客户端数据转发错误: %v", err)
		} else {
			log.Printf("后端->客户端数据转发完成，传输字节: %d", written)
		}
	}()

	wg.Wait()
	log.Printf("数据转发结束: %s <-> %s", clientAddr, backendAddr)
}

// wrappedConnection 包装连接，用于处理已读取的数据
type wrappedConnection struct {
	net.Conn
	prefixData []byte
	prefixRead bool
}

// Read 实现io.Reader接口
func (w *wrappedConnection) Read(b []byte) (n int, err error) {
	if !w.prefixRead && len(w.prefixData) > 0 {
		// 首次读取，返回预读的数据
		n = copy(b, w.prefixData)
		if n < len(w.prefixData) {
			// 缓冲区不够大，保留剩余数据
			w.prefixData = w.prefixData[n:]
		} else {
			// 所有预读数据已返回
			w.prefixRead = true
			w.prefixData = nil
		}
		return n, nil
	}

	// 预读数据已处理完，从原始连接读取
	return w.Conn.Read(b)
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	ClientAddr     string
	BackendAddr    string
	SNI            string
	StartTime      time.Time
	BytesToBackend int64
	BytesToClient  int64
	Status         string
}

// StatsManager 统计管理器
type StatsManager struct {
	connections map[string]*ConnectionStats
	mutex       sync.RWMutex
}

// NewStatsManager 创建统计管理器
func NewStatsManager() *StatsManager {
	return &StatsManager{
		connections: make(map[string]*ConnectionStats),
	}
}

// AddConnection 添加连接统计
func (sm *StatsManager) AddConnection(clientAddr, backendAddr, sni string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.connections[clientAddr] = &ConnectionStats{
		ClientAddr:  clientAddr,
		BackendAddr: backendAddr,
		SNI:         sni,
		StartTime:   time.Now(),
		Status:      "Active",
	}
}

// RemoveConnection 移除连接统计
func (sm *StatsManager) RemoveConnection(clientAddr string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if stats, exists := sm.connections[clientAddr]; exists {
		stats.Status = "Closed"
		// 可以选择立即删除或保留一段时间用于统计
		delete(sm.connections, clientAddr)
	}
}

// GetStats 获取统计信息
func (sm *StatsManager) GetStats() map[string]*ConnectionStats {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	stats := make(map[string]*ConnectionStats)
	for k, v := range sm.connections {
		stats[k] = v
	}
	return stats
}

// LogStats 记录统计信息
func (sm *StatsManager) LogStats() {
	stats := sm.GetStats()
	log.Printf("当前活跃连接数: %d", len(stats))
	for _, stat := range stats {
		duration := time.Since(stat.StartTime)
		log.Printf("连接: %s -> %s (SNI: %s), 持续时间: %v, 状态: %s",
			stat.ClientAddr, stat.BackendAddr, stat.SNI, duration, stat.Status)
	}
}
