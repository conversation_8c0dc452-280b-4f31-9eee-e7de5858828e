package proxy

import (
	"io"
	"log"
	"net"
	"sync"
	"time"

	"golang-rdp-proxy/internal/config"
	"golang-rdp-proxy/internal/rdp"
	"golang-rdp-proxy/internal/sni"
	tlsmgr "golang-rdp-proxy/internal/tls"
)

// Handler 代理处理器
type Handler struct {
	config     *config.Config
	tlsManager *tlsmgr.Manager
}

// NewHandler 创建代理处理器
func NewHandler(cfg *config.Config, tlsMgr *tlsmgr.Manager) *Handler {
	return &Handler{
		config:     cfg,
		tlsManager: tlsMgr,
	}
}

// HandleConnection 处理客户端连接
func (h *Handler) HandleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	clientAddr := clientConn.RemoteAddr().String()
	log.Printf("新客户端连接: %s", clientAddr)

	// 设置连接超时
	clientConn.SetReadDeadline(time.Now().Add(30 * time.Second))

	// 创建RDP协商器
	negotiator := rdp.NewNegotiator(clientConn)

	// 执行RDP协商
	if err := negotiator.HandleClientNegotiation(); err != nil {
		log.Printf("客户端 %s RDP协商失败: %v", clientAddr, err)
		return
	}

	// 重置读取超时
	clientConn.SetReadDeadline(time.Time{})

	// 现在客户端应该发送TLS ClientHello
	log.Printf("等待客户端TLS握手...")

	// 解析SNI
	sniValue, tlsData, err := sni.PeekSNI(clientConn)
	if err != nil {
		log.Printf("客户端 %s SNI解析失败: %v", clientAddr, err)
		return
	}

	log.Printf("客户端 %s SNI: %s", clientAddr, sniValue)

	// 验证SNI
	if !h.config.IsValidSNI(sniValue) {
		log.Printf("客户端 %s SNI无效或不在允许列表中: %s", clientAddr, sniValue)
		return
	}

	// 获取后端地址
	backendAddr, err := h.config.GetBackendAddr(sniValue)
	if err != nil {
		log.Printf("客户端 %s 获取后端地址失败: %v", clientAddr, err)
		return
	}

	log.Printf("客户端 %s 路由到后端: %s", clientAddr, backendAddr)

	// 创建一个包装连接来处理已读取的TLS数据
	wrappedConn := &wrappedConnection{
		Conn:       clientConn,
		prefixData: tlsData,
		prefixRead: false,
	}

	// 建立TLS连接到客户端
	clientTLSConn, err := h.tlsManager.WrapClientConnection(wrappedConn, sniValue)
	if err != nil {
		log.Printf("客户端 %s TLS握手失败: %v", clientAddr, err)
		return
	}

	log.Printf("客户端 %s TLS握手成功", clientAddr)

	// 建立到后端的连接
	backendConn, err := h.tlsManager.EstablishBackendRDPConnection(backendAddr)
	if err != nil {
		log.Printf("连接后端 %s 失败: %v", backendAddr, err)
		return
	}
	defer backendConn.Close()

	log.Printf("后端连接 %s 建立成功", backendAddr)

	// 开始双向数据转发
	h.relayData(clientTLSConn, backendConn, clientAddr, backendAddr)
}

// relayData 双向数据转发
func (h *Handler) relayData(clientConn, backendConn net.Conn, clientAddr, backendAddr string) {
	log.Printf("开始数据转发: %s <-> %s", clientAddr, backendAddr)

	var wg sync.WaitGroup
	wg.Add(2)

	// 客户端到后端
	go func() {
		defer wg.Done()
		defer backendConn.Close()

		written, err := io.Copy(backendConn, clientConn)
		if err != nil {
			log.Printf("客户端->后端数据转发错误: %v", err)
		} else {
			log.Printf("客户端->后端数据转发完成，传输字节: %d", written)
		}
	}()

	// 后端到客户端
	go func() {
		defer wg.Done()
		defer clientConn.Close()

		written, err := io.Copy(clientConn, backendConn)
		if err != nil {
			log.Printf("后端->客户端数据转发错误: %v", err)
		} else {
			log.Printf("后端->客户端数据转发完成，传输字节: %d", written)
		}
	}()

	wg.Wait()
	log.Printf("数据转发结束: %s <-> %s", clientAddr, backendAddr)
}

// wrappedConnection 包装连接，用于处理已读取的数据
type wrappedConnection struct {
	net.Conn
	prefixData []byte
	prefixRead bool
}

// Read 实现io.Reader接口
func (w *wrappedConnection) Read(b []byte) (n int, err error) {
	if !w.prefixRead && len(w.prefixData) > 0 {
		// 首次读取，返回预读的数据
		n = copy(b, w.prefixData)
		if n < len(w.prefixData) {
			// 缓冲区不够大，保留剩余数据
			w.prefixData = w.prefixData[n:]
		} else {
			// 所有预读数据已返回
			w.prefixRead = true
			w.prefixData = nil
		}
		return n, nil
	}

	// 预读数据已处理完，从原始连接读取
	return w.Conn.Read(b)
}

// ConnectionStats 连接统计
type ConnectionStats struct {
	ClientAddr     string
	BackendAddr    string
	SNI            string
	StartTime      time.Time
	BytesToBackend int64
	BytesToClient  int64
	Status         string
}

// StatsManager 统计管理器
type StatsManager struct {
	connections map[string]*ConnectionStats
	mutex       sync.RWMutex
}

// NewStatsManager 创建统计管理器
func NewStatsManager() *StatsManager {
	return &StatsManager{
		connections: make(map[string]*ConnectionStats),
	}
}

// AddConnection 添加连接统计
func (sm *StatsManager) AddConnection(clientAddr, backendAddr, sni string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	sm.connections[clientAddr] = &ConnectionStats{
		ClientAddr:  clientAddr,
		BackendAddr: backendAddr,
		SNI:         sni,
		StartTime:   time.Now(),
		Status:      "Active",
	}
}

// RemoveConnection 移除连接统计
func (sm *StatsManager) RemoveConnection(clientAddr string) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	if stats, exists := sm.connections[clientAddr]; exists {
		stats.Status = "Closed"
		// 可以选择立即删除或保留一段时间用于统计
		delete(sm.connections, clientAddr)
	}
}

// GetStats 获取统计信息
func (sm *StatsManager) GetStats() map[string]*ConnectionStats {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	stats := make(map[string]*ConnectionStats)
	for k, v := range sm.connections {
		stats[k] = v
	}
	return stats
}

// LogStats 记录统计信息
func (sm *StatsManager) LogStats() {
	stats := sm.GetStats()
	log.Printf("当前活跃连接数: %d", len(stats))
	for _, stat := range stats {
		duration := time.Since(stat.StartTime)
		log.Printf("连接: %s -> %s (SNI: %s), 持续时间: %v, 状态: %s",
			stat.ClientAddr, stat.BackendAddr, stat.SNI, duration, stat.Status)
	}
}
