@echo off
title RDP SNI代理服务器

echo ========================================
echo RDP SNI代理服务器启动脚本
echo ========================================
echo.

REM 检查可执行文件
if not exist rdp-proxy.exe (
    echo 错误: 未找到rdp-proxy.exe，请先运行build.bat编译程序
    echo.
    pause
    exit /b 1
)

REM 检查证书文件
if not exist certs\server.crt (
    echo 警告: 未找到TLS证书文件 certs\server.crt
    echo 是否要生成测试证书？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        call generate-cert.bat
        if %errorlevel% neq 0 (
            echo 证书生成失败，程序退出
            pause
            exit /b 1
        )
    ) else (
        echo 请手动放置证书文件后重新运行
        pause
        exit /b 1
    )
)

if not exist certs\server.key (
    echo 错误: 未找到TLS私钥文件 certs\server.key
    echo 请确保证书文件完整
    pause
    exit /b 1
)

echo 证书文件检查通过
echo.

echo 当前配置的SNI路由规则:
echo   20201.rdp.anan.cc -^> 127.0.0.1:20201
echo   127.rdp.anan.cc   -^> 127.0.0.1:1111
echo.

echo 启动RDP代理服务器...
echo 监听端口: 3389
echo 按Ctrl+C停止服务器
echo.

REM 启动服务器
rdp-proxy.exe

echo.
echo 服务器已停止
pause
