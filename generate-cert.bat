@echo off
echo 生成测试用TLS证书...

REM 检查OpenSSL
openssl version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到OpenSSL，请先安装OpenSSL
    echo 可以从以下地址下载: https://slproweb.com/products/Win32OpenSSL.html
    pause
    exit /b 1
)

REM 创建certs目录
if not exist certs mkdir certs

REM 生成自签名证书
echo 生成自签名证书...
openssl req -x509 -newkey rsa:2048 -keyout certs/server.key -out certs/server.crt -days 365 -nodes -subj "/C=CN/ST=Beijing/L=Beijing/O=RDP-Proxy/CN=*.rdp.anan.cc"

if %errorlevel% neq 0 (
    echo 错误: 证书生成失败
    pause
    exit /b 1
)

echo 证书生成成功！
echo 证书文件: certs/server.crt
echo 私钥文件: certs/server.key
echo.
echo 注意: 这是测试用的自签名证书，生产环境请使用正式CA签发的证书
echo.
pause
