# RDP SNI代理服务器使用指南

## 概述

本代理服务器通过解析TLS ClientHello中的SNI（Server Name Indication）字段，实现基于域名的RDP连接路由。它作为中间人代理，完整参与RDP协商过程，并建立双重TLS连接以确保安全隔离。

## 工作原理

### 1. 连接流程

```
1. 客户端连接到代理服务器的3389端口
2. 代理服务器与客户端进行RDP协商（X.224、MCS、GCC）
3. 客户端发起TLS握手，携带SNI信息
4. 代理服务器解析SNI，确定路由目标
5. 代理服务器与客户端完成TLS握手（第一段TLS）
6. 代理服务器连接到后端RDP服务器
7. 代理服务器与后端服务器建立TLS连接（第二段TLS）
8. 开始双向数据转发
```

### 2. 安全机制

- **SNI验证**: 只允许预定义的SNI域名连接
- **TLS终止**: 在代理层终止客户端TLS连接
- **重新加密**: 与后端建立新的TLS连接
- **协议隔离**: 客户端和后端使用独立的加密通道

## 安装和配置

### 1. 系统要求

- Windows 10/11 或 Windows Server 2016+
- Go 1.21+ （用于编译）
- OpenSSL （用于生成证书）

### 2. 编译安装

```bash
# 克隆或下载项目代码
cd golang-rdp-proxy

# 编译程序
go mod tidy
go build -o rdp-proxy.exe cmd/main.go
```

或者直接运行构建脚本：
```bash
build.bat
```

### 3. 证书配置

#### 方法1: 使用现有证书
将您的TLS证书文件复制到certs目录：
- `certs/server.crt` - 服务器证书
- `certs/server.key` - 服务器私钥

#### 方法2: 生成测试证书
运行证书生成脚本：
```bash
generate-cert.bat
```

这将生成一个通配符证书 `*.rdp.anan.cc`，适用于测试环境。

### 4. 路由配置

在 `internal/config/config.go` 中修改SNI路由规则：

```go
SNIRoutes: map[string]string{
    "20201.rdp.anan.cc": "127.0.0.1:20201",  // 路由到本地20201端口
    "127.rdp.anan.cc":   "127.0.0.1:1111",   // 路由到本地1111端口
    // 添加更多路由规则...
    "prod.rdp.anan.cc":  "192.168.1.100:3389",
},
```

## 运行服务器

### 1. 直接运行

```bash
rdp-proxy.exe
```

### 2. 使用启动脚本

```bash
start.bat
```

启动脚本会自动检查证书文件，如果不存在会提示生成测试证书。

### 3. 服务器输出

正常启动后会看到类似输出：
```
2024/01/01 12:00:00 RDP SNI代理服务器
2024/01/01 12:00:00 版本: 1.0.0
2024/01/01 12:00:00 启动RDP SNI代理服务器...
2024/01/01 12:00:00 RDP Proxy Server Configuration:
2024/01/01 12:00:00   Listen: 0.0.0.0:3389
2024/01/01 12:00:00   TLS Cert: certs/server.crt
2024/01/01 12:00:00   TLS Key: certs/server.key
2024/01/01 12:00:00   SNI Routes:
2024/01/01 12:00:00     20201.rdp.anan.cc -> 127.0.0.1:20201
2024/01/01 12:00:00     127.rdp.anan.cc -> 127.0.0.1:1111
2024/01/01 12:00:00 TLS证书加载成功
2024/01/01 12:00:00 RDP代理服务器启动成功，监听地址: 0.0.0.0:3389
```

## 客户端连接

### 1. 使用mstsc连接

```bash
# 连接到第一个后端（20201端口）
mstsc /v:20201.rdp.anan.cc:3389

# 连接到第二个后端（1111端口）
mstsc /v:127.rdp.anan.cc:3389
```

### 2. 使用RDP客户端

在RDP客户端中输入：
- 服务器地址: `20201.rdp.anan.cc:3389` 或 `127.rdp.anan.cc:3389`
- 用户名和密码: 使用后端RDP服务器的凭据

### 3. 连接要求

- 客户端必须支持SNI（新版本的mstsc都支持）
- 客户端必须支持TLS 1.2+
- 域名必须在代理服务器的路由表中

## 后端服务器配置

### 1. RDP服务器要求

- 必须启用TLS加密
- 建议使用RDP 8.0+版本
- 确保监听指定的端口

### 2. Windows RDP服务器配置

1. 启用远程桌面：
   - 控制面板 → 系统 → 远程设置
   - 勾选"启用远程桌面"

2. 配置TLS：
   - 运行 `gpedit.msc`
   - 导航到：计算机配置 → 管理模板 → Windows组件 → 远程桌面服务 → 远程桌面会话主机 → 安全
   - 启用"要求使用特定的安全层进行远程(RDP)连接"
   - 设置为"SSL"

3. 配置端口（如果需要非标准端口）：
   - 注册表编辑器中修改：
   - `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Terminal Server\WinStations\RDP-Tcp\PortNumber`

## 监控和日志

### 1. 连接日志

代理服务器会记录详细的连接信息：
```
2024/01/01 12:05:00 新客户端连接: 192.168.1.100:12345
2024/01/01 12:05:00 开始RDP协商处理
2024/01/01 12:05:00 收到X.224连接请求: SrcRef=1234, DstRef=0
2024/01/01 12:05:00 发送X.224连接确认，选择协议: 0x00000001
2024/01/01 12:05:00 客户端 192.168.1.100:12345 SNI: 20201.rdp.anan.cc
2024/01/01 12:05:00 客户端 192.168.1.100:12345 路由到后端: 127.0.0.1:20201
2024/01/01 12:05:00 客户端 192.168.1.100:12345 TLS握手成功
2024/01/01 12:05:00 后端连接 127.0.0.1:20201 建立成功
2024/01/01 12:05:00 开始数据转发: 192.168.1.100:12345 <-> 127.0.0.1:20201
```

### 2. 统计信息

每5分钟输出一次连接统计：
```
2024/01/01 12:10:00 当前活跃连接数: 2
2024/01/01 12:10:00 连接: 192.168.1.100:12345 -> 127.0.0.1:20201 (SNI: 20201.rdp.anan.cc), 持续时间: 5m0s, 状态: Active
```

## 故障排除

### 1. 连接被拒绝

**症状**: 客户端无法连接，代理服务器日志显示"SNI无效"

**解决方案**:
- 检查客户端使用的域名是否在路由表中
- 确认客户端支持SNI
- 验证域名拼写是否正确

### 2. TLS握手失败

**症状**: 连接建立后立即断开，日志显示TLS握手失败

**解决方案**:
- 检查证书文件是否正确
- 确认证书包含所需的域名
- 验证私钥与证书匹配

### 3. 后端连接失败

**症状**: SNI解析成功但无法连接到后端

**解决方案**:
- 检查后端RDP服务器是否运行
- 确认网络连通性
- 验证端口配置是否正确

### 4. 数据转发异常

**症状**: 连接建立但RDP会话异常

**解决方案**:
- 检查后端RDP服务器TLS配置
- 确认RDP协议版本兼容性
- 查看详细的错误日志

## 高级配置

### 1. 修改监听端口

在 `internal/config/config.go` 中修改：
```go
ListenPort: 13389, // 使用非标准端口
```

### 2. 添加更多路由

```go
SNIRoutes: map[string]string{
    "server1.rdp.domain.com": "10.0.1.100:3389",
    "server2.rdp.domain.com": "10.0.1.101:3389",
    "dev.rdp.domain.com":     "192.168.1.50:3389",
},
```

### 3. 启用详细日志

在 `cmd/main.go` 中修改：
```go
log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)
```

## 性能优化

### 1. 系统调优

- 增加系统文件描述符限制
- 优化TCP缓冲区大小
- 启用TCP快速打开（如果支持）

### 2. 代理调优

- 调整数据转发缓冲区大小
- 优化连接超时设置
- 实现连接池机制

## 安全建议

1. **网络隔离**: 将代理服务器部署在DMZ区域
2. **访问控制**: 使用防火墙限制客户端来源
3. **证书管理**: 定期更新TLS证书
4. **日志监控**: 监控异常连接和访问模式
5. **定期更新**: 保持软件版本最新

## 支持的客户端

- Windows 远程桌面连接 (mstsc) - Windows 10/11
- Remote Desktop Connection Manager (RDCMan)
- FreeRDP 2.0+
- Remmina（Linux）
- Microsoft Remote Desktop（macOS/iOS/Android）

注意：客户端必须支持SNI扩展，旧版本的RDP客户端可能不支持。
