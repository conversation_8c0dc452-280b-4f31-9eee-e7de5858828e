# RDP SNI代理服务器配置文件

# 服务器监听配置
server:
  # 监听地址，0.0.0.0表示监听所有网卡
  listen_addr: "0.0.0.0"
  # 监听端口，RDP默认端口为3389
  listen_port: 3389

# TLS证书配置
tls:
  # 证书文件路径
  cert_file: "certs/server.crt"
  # 私钥文件路径
  key_file: "certs/server.key"

# SNI路由规则
# 格式: "SNI域名": "后端地址:端口"
sni_routes:
  "20201.rdp.anan.cc": "127.0.0.1:20201"
  "127.rdp.anan.cc": "127.0.0.1:1111"
  # 可以添加更多路由规则
  # "example.rdp.domain.com": "192.168.1.100:3389"

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARN, ERROR
  level: "INFO"
  # 是否启用详细日志
  verbose: true
  # 日志文件路径（可选，不设置则输出到控制台）
  # file: "logs/rdp-proxy.log"

# 安全配置
security:
  # 连接超时时间（秒）
  connection_timeout: 30
  # 最大并发连接数
  max_connections: 1000
  # 是否启用连接限流
  enable_rate_limit: false
  # 每秒最大新连接数
  max_connections_per_second: 10

# 性能配置
performance:
  # 数据转发缓冲区大小（字节）
  buffer_size: 32768
  # 是否启用TCP_NODELAY
  tcp_nodelay: true
  # 是否启用SO_KEEPALIVE
  keep_alive: true
