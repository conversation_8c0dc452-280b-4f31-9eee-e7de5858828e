@echo off
title RDP代理连接测试

echo ========================================
echo RDP SNI代理服务器连接测试
echo ========================================
echo.

REM 检查代理服务器是否运行
echo 检查代理服务器状态...
netstat -an | findstr ":3389" >nul
if %errorlevel% neq 0 (
    echo 错误: 代理服务器未在3389端口监听
    echo 请先启动rdp-proxy.exe
    echo.
    pause
    exit /b 1
)

echo 代理服务器正在运行 ✓
echo.

echo 可用的测试连接:
echo.
echo 1. 测试连接到 20201.rdp.anan.cc (后端: 127.0.0.1:20201)
echo 2. 测试连接到 127.rdp.anan.cc (后端: 127.0.0.1:1111)
echo 3. 测试无效SNI连接 (应该被拒绝)
echo 4. 退出
echo.

:menu
set /p choice=请选择测试选项 (1-4): 

if "%choice%"=="1" goto test1
if "%choice%"=="2" goto test2
if "%choice%"=="3" goto test3
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:test1
echo.
echo 测试连接到 20201.rdp.anan.cc...
echo 检查后端服务器 127.0.0.1:20201...
netstat -an | findstr "127.0.0.1:20201" >nul
if %errorlevel% neq 0 (
    echo 警告: 后端服务器 127.0.0.1:20201 可能未运行
    echo 连接可能会失败
)
echo.
echo 启动远程桌面连接...
echo 如果出现证书警告，请选择"是"继续连接
mstsc /v:20201.rdp.anan.cc:3389
goto menu

:test2
echo.
echo 测试连接到 127.rdp.anan.cc...
echo 检查后端服务器 127.0.0.1:1111...
netstat -an | findstr "127.0.0.1:1111" >nul
if %errorlevel% neq 0 (
    echo 警告: 后端服务器 127.0.0.1:1111 可能未运行
    echo 连接可能会失败
)
echo.
echo 启动远程桌面连接...
echo 如果出现证书警告，请选择"是"继续连接
mstsc /v:127.rdp.anan.cc:3389
goto menu

:test3
echo.
echo 测试无效SNI连接...
echo 这个连接应该被代理服务器拒绝
echo.
echo 尝试连接到 invalid.rdp.test.com...
mstsc /v:invalid.rdp.test.com:3389
echo.
echo 如果连接被拒绝，说明SNI验证工作正常
goto menu

:exit
echo.
echo 测试结束
pause
exit /b 0
