package config

import (
	"fmt"
	"log"
)

// Config 代理服务器配置
type Config struct {
	// 监听配置
	ListenAddr string
	ListenPort int

	// TLS证书配置
	CertFile string
	KeyFile  string

	// SNI路由规则
	SNIRoutes map[string]string

	// 日志配置
	LogLevel string
}

// NewConfig 创建默认配置
func NewConfig() *Config {
	return &Config{
		ListenAddr: "0.0.0.0",
		ListenPort: 3389,
		CertFile:   "certs/server.crt",
		KeyFile:    "certs/server.key",
		SNIRoutes: map[string]string{
			"20201.rdp.anan.cc": "127.0.0.1:20201",
			"127.rdp.anan.cc":   "39.172.91.235:20201",
		},
		LogLevel: "INFO",
	}
}

// GetBackendAddr 根据SNI获取后端地址
func (c *Config) GetBackendAddr(sni string) (string, error) {
	if addr, exists := c.SNIRoutes[sni]; exists {
		return addr, nil
	}
	return "", fmt.Errorf("SNI %s not found in routing table", sni)
}

// IsValidSNI 检查SNI是否有效
func (c *Config) IsValidSNI(sni string) bool {
	_, exists := c.SNIRoutes[sni]
	return exists
}

// LogConfig 打印配置信息
func (c *Config) LogConfig() {
	log.Printf("RDP Proxy Server Configuration:")
	log.Printf("  Listen: %s:%d", c.ListenAddr, c.ListenPort)
	log.Printf("  TLS Cert: %s", c.CertFile)
	log.Printf("  TLS Key: %s", c.KeyFile)
	log.Printf("  SNI Routes:")
	for sni, backend := range c.SNIRoutes {
		log.Printf("    %s -> %s", sni, backend)
	}
	log.Printf("  Log Level: %s", c.LogLevel)
}
