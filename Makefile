# RDP SNI代理服务器 Makefile

# 变量定义
BINARY_NAME=rdp-proxy
BINARY_WINDOWS=$(BINARY_NAME).exe
MAIN_PATH=cmd/main.go
BUILD_DIR=build
VERSION=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go编译参数
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 默认目标
.PHONY: all
all: clean build

# 编译
.PHONY: build
build:
	@echo "编译RDP代理服务器..."
	go mod tidy
	go build $(LDFLAGS) -o $(BINARY_WINDOWS) $(MAIN_PATH)
	@echo "编译完成: $(BINARY_WINDOWS)"

# 清理
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@if exist $(BINARY_WINDOWS) del $(BINARY_WINDOWS)
	@if exist $(BUILD_DIR) rmdir /s /q $(BUILD_DIR)
	@echo "清理完成"

# 测试
.PHONY: test
test:
	@echo "运行测试..."
	go test -v ./test/...
	@echo "测试完成"

# 生成证书
.PHONY: cert
cert:
	@echo "生成测试证书..."
	@if not exist certs mkdir certs
	openssl req -x509 -newkey rsa:2048 -keyout certs/server.key -out certs/server.crt -days 365 -nodes -subj "/C=CN/ST=Beijing/L=Beijing/O=RDP-Proxy/CN=*.rdp.anan.cc"
	@echo "证书生成完成"

# 运行
.PHONY: run
run: build
	@echo "启动RDP代理服务器..."
	./$(BINARY_WINDOWS)

# 安装依赖
.PHONY: deps
deps:
	@echo "下载依赖..."
	go mod download
	go mod tidy
	@echo "依赖安装完成"

# 格式化代码
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	go fmt ./...
	@echo "代码格式化完成"

# 代码检查
.PHONY: vet
vet:
	@echo "代码检查..."
	go vet ./...
	@echo "代码检查完成"

# 完整检查
.PHONY: check
check: fmt vet test
	@echo "所有检查完成"

# 发布构建
.PHONY: release
release: clean check
	@echo "构建发布版本..."
	@if not exist $(BUILD_DIR) mkdir $(BUILD_DIR)
	go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_WINDOWS) $(MAIN_PATH)
	@echo "发布版本构建完成: $(BUILD_DIR)/$(BINARY_WINDOWS)"

# 帮助
.PHONY: help
help:
	@echo "可用的make目标:"
	@echo "  build    - 编译程序"
	@echo "  clean    - 清理构建文件"
	@echo "  test     - 运行测试"
	@echo "  cert     - 生成测试证书"
	@echo "  run      - 编译并运行"
	@echo "  deps     - 安装依赖"
	@echo "  fmt      - 格式化代码"
	@echo "  vet      - 代码检查"
	@echo "  check    - 完整检查(fmt+vet+test)"
	@echo "  release  - 构建发布版本"
	@echo "  help     - 显示此帮助信息"
