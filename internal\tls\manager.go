package tls

import (
	"crypto/tls"
	"fmt"
	"log"
	"net"
)

// Manager TLS管理器
type Manager struct {
	certFile string
	keyFile  string
	cert     tls.Certificate
}

// NewManager 创建TLS管理器
func NewManager(certFile, keyFile string) (*Manager, error) {
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		return nil, fmt.Errorf("加载TLS证书失败: %v", err)
	}

	return &Manager{
		certFile: certFile,
		keyFile:  keyFile,
		cert:     cert,
	}, nil
}

// WrapClientConnection 包装客户端连接为TLS连接
func (m *Manager) WrapClientConnection(conn net.Conn, sni string) (*tls.Conn, error) {
	log.Printf("为客户端连接启动TLS握手，SNI: %s", sni)

	// 创建TLS配置
	config := &tls.Config{
		Certificates: []tls.Certificate{m.cert},
		ServerName:   sni,
		MinVersion:   tls.VersionTLS12,
		MaxVersion:   tls.VersionTLS13,
	}

	// 包装为TLS服务器连接
	tlsConn := tls.Server(conn, config)

	// 执行TLS握手
	if err := tlsConn.Handshake(); err != nil {
		return nil, fmt.Errorf("客户端TLS握手失败: %v", err)
	}

	log.Printf("客户端TLS握手成功")
	return tlsConn, nil
}

// ConnectToBackend 连接到后端服务器并建立TLS连接
func (m *Manager) ConnectToBackend(backendAddr string) (*tls.Conn, error) {
	log.Printf("连接到后端服务器: %s", backendAddr)

	// 连接到后端服务器
	conn, err := net.Dial("tcp", backendAddr)
	if err != nil {
		return nil, fmt.Errorf("连接后端服务器失败: %v", err)
	}

	// 创建TLS客户端配置
	config := &tls.Config{
		InsecureSkipVerify: true, // 在生产环境中应该验证证书
		MinVersion:         tls.VersionTLS12,
		MaxVersion:         tls.VersionTLS13,
	}

	// 包装为TLS客户端连接
	tlsConn := tls.Client(conn, config)

	// 执行TLS握手
	if err := tlsConn.Handshake(); err != nil {
		conn.Close()
		return nil, fmt.Errorf("后端TLS握手失败: %v", err)
	}

	log.Printf("后端TLS握手成功")
	return tlsConn, nil
}

// EstablishBackendRDPConnection 建立到后端的完整RDP连接
func (m *Manager) EstablishBackendRDPConnection(backendAddr string) (net.Conn, error) {
	log.Printf("建立到后端的RDP连接: %s", backendAddr)

	// 连接到后端服务器
	conn, err := net.Dial("tcp", backendAddr)
	if err != nil {
		return nil, fmt.Errorf("连接后端服务器失败: %v", err)
	}

	// 执行RDP协商
	if err := m.performBackendRDPNegotiation(conn); err != nil {
		conn.Close()
		return nil, fmt.Errorf("后端RDP协商失败: %v", err)
	}

	// 建立TLS连接
	config := &tls.Config{
		InsecureSkipVerify: true, // 在生产环境中应该验证证书
		MinVersion:         tls.VersionTLS12,
		MaxVersion:         tls.VersionTLS13,
	}

	tlsConn := tls.Client(conn, config)
	if err := tlsConn.Handshake(); err != nil {
		conn.Close()
		return nil, fmt.Errorf("后端TLS握手失败: %v", err)
	}

	log.Printf("后端RDP+TLS连接建立成功")
	return tlsConn, nil
}

// performBackendRDPNegotiation 执行后端RDP协商
func (m *Manager) performBackendRDPNegotiation(conn net.Conn) error {
	// 发送X.224连接请求
	x224CR := []byte{
		0x03, 0x00, 0x00, 0x2B, // TPKT Header (length = 43)
		0x26,       // X.224 Length
		0xE0,       // X.224 Connection Request
		0x00, 0x00, // Dst Ref
		0x00, 0x00, // Src Ref
		0x00, // Class
		// Cookie
		'C', 'o', 'o', 'k', 'i', 'e', ':', ' ', 'm', 's', 't', 's', 'h', 'a', 's', 'h', '=', 'u', 's', 'e', 'r', '\r', '\n',
		// RDP Negotiation Request
		0x01,       // Type
		0x00,       // Flags
		0x08, 0x00, // Length
		0x01, 0x00, 0x00, 0x00, // Requested Protocols (SSL)
	}

	if _, err := conn.Write(x224CR); err != nil {
		return fmt.Errorf("发送X.224连接请求失败: %v", err)
	}

	// 读取X.224连接确认
	response := make([]byte, 1024)
	n, err := conn.Read(response)
	if err != nil {
		return fmt.Errorf("读取X.224连接确认失败: %v", err)
	}

	if n < 7 || response[5] != 0xD0 {
		return fmt.Errorf("无效的X.224连接确认")
	}

	log.Printf("后端X.224连接建立成功")

	// 为了简化，我们跳过完整的MCS协商
	// 在实际实现中，需要完整的MCS Connect-Initial/Response交换
	// 这里直接假设MCS协商成功

	log.Printf("后端RDP协商完成")
	return nil
}

// GetCertificate 获取证书信息
func (m *Manager) GetCertificate() *tls.Certificate {
	return &m.cert
}

// ValidateSNI 验证SNI是否与证书匹配
func (m *Manager) ValidateSNI(sni string) bool {
	// 简化的SNI验证
	// 在实际实现中，应该检查证书的Subject Alternative Names
	return sni != ""
}

// CreateTLSConfig 创建TLS配置
func (m *Manager) CreateTLSConfig(sni string) *tls.Config {
	return &tls.Config{
		Certificates: []tls.Certificate{m.cert},
		ServerName:   sni,
		MinVersion:   tls.VersionTLS12,
		MaxVersion:   tls.VersionTLS13,
		// 可以添加更多安全配置
		CipherSuites: []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
		},
		PreferServerCipherSuites: true,
	}
}
