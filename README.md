# RDP SNI 代理服务器

基于SNI的RDP连接路由代理服务器，实现TLS终止和重新握手的中间人代理模式。

## 功能特性

- **SNI路由**: 根据TLS ClientHello中的SNI字段进行域名路由
- **RDP协议MITM**: 完整参与RDP协商过程（X.224、MCS、GCC）
- **双重TLS握手**: 客户端到代理、代理到后端的独立TLS连接
- **安全防护**: 拒绝无SNI或不匹配SNI的连接
- **高性能**: 基于Go语言的高并发处理

## 架构设计

```
客户端(MSTSC) 
    ↓ TLS握手（携带SNI）
[代理服务器: TLS终止 + 根据SNI路由]
    ↓ 重新发起TLS握手  
    ↓ 转发RDP流量
[真实RDP后端服务器]
```

## 快速开始

### 1. 准备证书

将您的TLS证书文件放置在 `certs/` 目录下：
- `certs/server.crt` - 服务器证书
- `certs/server.key` - 服务器私钥

或者生成测试用的自签名证书：
```bash
openssl req -x509 -newkey rsa:2048 -keyout certs/server.key -out certs/server.crt -days 365 -nodes \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Test/CN=*.rdp.anan.cc"
```

### 2. 编译运行

```bash
# 下载依赖
go mod tidy

# 编译
go build -o rdp-proxy cmd/main.go

# 运行
./rdp-proxy
```

### 3. 配置路由

默认路由配置（在 `internal/config/config.go` 中）：
```
20201.rdp.anan.cc → 127.0.0.1:20201
127.rdp.anan.cc   → 127.0.0.1:1111
```

## 使用方法

### 客户端连接

使用支持SNI的RDP客户端（如新版本的mstsc）连接：

```bash
# 连接到第一个后端
mstsc /v:20201.rdp.anan.cc:3389

# 连接到第二个后端  
mstsc /v:127.rdp.anan.cc:3389
```

### 后端服务器

确保后端RDP服务器正在运行并监听相应端口：
- `127.0.0.1:20201` - 第一个RDP服务器
- `127.0.0.1:1111` - 第二个RDP服务器

## 项目结构

```
golang-rdp-proxy/
├── cmd/
│   └── main.go              # 主程序入口
├── internal/
│   ├── config/
│   │   └── config.go        # 配置管理
│   ├── sni/
│   │   └── parser.go        # SNI解析器
│   ├── rdp/
│   │   ├── protocol.go      # RDP协议定义
│   │   └── negotiator.go    # RDP协商处理
│   ├── proxy/
│   │   └── handler.go       # 代理处理器
│   └── tls/
│       └── manager.go       # TLS管理器
├── certs/
│   ├── server.crt           # TLS证书
│   ├── server.key           # TLS私钥
│   └── README.md            # 证书说明
├── docs/
│   └── design.md            # 设计文档
├── go.mod
├── go.sum
└── README.md
```

## 配置说明

### 监听配置
- 默认监听地址: `0.0.0.0:3389`
- 可在 `config.go` 中修改

### SNI路由规则
在 `internal/config/config.go` 中的 `SNIRoutes` 映射中配置：
```go
SNIRoutes: map[string]string{
    "20201.rdp.anan.cc": "127.0.0.1:20201",
    "127.rdp.anan.cc":   "127.0.0.1:1111",
    // 添加更多路由规则...
},
```

### TLS证书
- 证书文件路径: `certs/server.crt`
- 私钥文件路径: `certs/server.key`
- 支持PEM格式证书

## 安全特性

1. **SNI验证**: 严格验证SNI字段，拒绝无效连接
2. **协议隔离**: 客户端和后端使用独立的TLS连接
3. **连接隔离**: 不同客户端连接完全隔离
4. **日志审计**: 详细的连接和路由日志

## 性能特性

- **高并发**: 基于Go协程的并发处理
- **低延迟**: 高效的数据转发机制
- **内存优化**: 合理的缓冲区管理
- **连接复用**: 支持长连接和连接复用

## 故障排除

### 常见问题

1. **证书错误**
   - 检查证书文件是否存在且格式正确
   - 确保证书包含所需的SNI域名

2. **连接被拒绝**
   - 检查SNI是否在允许列表中
   - 确认客户端发送了正确的SNI

3. **后端连接失败**
   - 检查后端服务器是否运行
   - 确认网络连通性和端口开放

### 日志分析

程序会输出详细的日志信息，包括：
- 客户端连接信息
- SNI解析结果
- 路由决策过程
- TLS握手状态
- 数据转发统计

## 开发说明

### 添加新的SNI路由

1. 在 `internal/config/config.go` 中添加路由规则
2. 确保证书支持新的域名
3. 重启服务器使配置生效

### 扩展功能

- 动态配置重载
- 负载均衡支持
- 健康检查机制
- 监控和指标收集

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
