package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"strings"
	"sync"
)

//------------------------------------------------------
// 1) 全局配置：域名 -> 后端地址(ip:port)
//    用读写锁保护，支持通过HTTP接口进行CRUD
//------------------------------------------------------

type BackendConfig struct {
	sync.RWMutex
	DomainToAddr map[string]string // key: 域名(小写), value: "ip:port"
}

var globalBackendConfig = &BackendConfig{
	DomainToAddr: map[string]string{
		"127.rdp.anan.cc": "127.0.0.1:1111",
	},
}

//------------------------------------------------------
// 2) HTTP API，用于管理域名->后端的映射关系
//------------------------------------------------------

func startHTTPAPI(addr string) {
	mux := http.NewServeMux()

	// GET /config  查询全部映射
	mux.HandleFunc("/config", func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			globalBackendConfig.RLock()
			defer globalBackendConfig.RUnlock()
			json.NewEncoder(w).Encode(globalBackendConfig.DomainToAddr)

		case http.MethodPost:
			// body: { "domain": "rdp.example.com", "backend": "**************:3389" }
			var req struct {
				Domain  string `json:"domain"`
				Backend string `json:"backend"`
			}
			if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
				http.Error(w, "Invalid JSON", http.StatusBadRequest)
				return
			}
			d := strings.ToLower(strings.TrimSpace(req.Domain))
			b := strings.TrimSpace(req.Backend)
			if d == "" || b == "" {
				http.Error(w, "domain and backend required", http.StatusBadRequest)
				return
			}

			globalBackendConfig.Lock()
			globalBackendConfig.DomainToAddr[d] = b
			globalBackendConfig.Unlock()

			fmt.Fprintf(w, "added/updated: %s -> %s\n", d, b)

		case http.MethodDelete:
			// ?domain=rdp.example.com
			q := r.URL.Query()
			d := strings.ToLower(strings.TrimSpace(q.Get("domain")))
			if d == "" {
				http.Error(w, "domain is empty", http.StatusBadRequest)
				return
			}
			globalBackendConfig.Lock()
			delete(globalBackendConfig.DomainToAddr, d)
			globalBackendConfig.Unlock()
			fmt.Fprintf(w, "deleted: %s\n", d)

		default:
			http.Error(w, "method not allowed", http.StatusMethodNotAllowed)
		}
	})

	server := &http.Server{
		Addr:    addr,
		Handler: mux,
	}
	log.Printf("[HTTP API] 启动监听 %s", addr)
	log.Fatal(server.ListenAndServe())
}

//------------------------------------------------------
// 3) RDP协议相关辅助
//    - 读取 X.224 Connection Request
//    - 发送 X.224 Connection Confirm (TLS)
//    - (简化) 不做完整RDP解析
//------------------------------------------------------

// X224ConnectionRequest 仅示例记录一下 TPKT 总长度
type X224ConnectionRequest struct {
	TPKTTotalLength uint16
}

func readX224ConnectionRequest(conn net.Conn) (*X224ConnectionRequest, []byte, error) {
	header := make([]byte, 4)
	if _, err := io.ReadFull(conn, header); err != nil {
		return nil, nil, err
	}
	if header[0] != 3 {
		return nil, nil, fmt.Errorf("not a valid TPKT version=3")
	}
	totalLen := uint16(header[2])<<8 | uint16(header[3])
	if totalLen < 4 {
		return nil, nil, fmt.Errorf("invalid totalLen < 4")
	}

	bodyLen := int(totalLen) - 4
	body := make([]byte, bodyLen)
	if _, err := io.ReadFull(conn, body); err != nil {
		return nil, nil, err
	}

	req := &X224ConnectionRequest{
		TPKTTotalLength: totalLen,
	}
	return req, body, nil
}

func writeX224ConnectionConfirm(conn net.Conn) error {
	// 简化：只返回 X.224 CC + RDP_NEG_RSP(选TLS=0x01)
	// X.224 CC: 固定7字节
	x224CC := []byte{0x05, 0xd0, 0x00, 0x00, 0x00, 0x00, 0x00}

	// RDP Negotiation Response (8字节):
	//   type=2, length=8, selectedProtocol=TLS(0x01)
	rdpNegResp := []byte{
		0x02, 0x00, 0x08, 0x00,
		0x01, 0x00, 0x00, 0x00, // selectedProtocol=1 (TLS)
	}

	totalLen := 4 + len(x224CC) + len(rdpNegResp)
	tpkt := []byte{
		0x03, 0x00,
		byte(totalLen >> 8), byte(totalLen & 0xff),
	}
	packet := append(tpkt, x224CC...)
	packet = append(packet, rdpNegResp...)
	_, err := conn.Write(packet)
	return err
}

//------------------------------------------------------
// 4) 代理端：充当“RDP服务器”接收客户端连接
//    - 完成 X.224
//    - 升级TLS (从 ClientHello 解析SNI)
//    - 找到后端目标后，去连后端 (也要发起X.224+TLS)，然后做流量转发
//------------------------------------------------------

// 用于加载（代理端）证书时的全局配置
var tlsConfigForClients *tls.Config

// 用于拨号后端时，使用的TLS配置（可设置InsecureSkipVerify等）
var tlsConfigForBackends *tls.Config

func startRDPProxy(addr string) {
	ln, err := net.Listen("tcp", addr)
	if err != nil {
		log.Fatalf("监听 %s 失败: %v", addr, err)
	}
	log.Printf("[RDP Proxy] 启动监听 %s", addr)

	for {
		clientConn, err := ln.Accept()
		if err != nil {
			log.Printf("Accept 失败: %v", err)
			continue
		}
		log.Printf("[RDP Proxy] 收到来自 %s 的连接", clientConn.RemoteAddr())

		go handleClientConn(clientConn)
	}
}

func handleClientConn(c net.Conn) {
	defer c.Close()

	//----------------------------------
	// 第一步：读取客户端发来的 X.224 CR
	//----------------------------------
	req, body, err := readX224ConnectionRequest(c)
	if err != nil {
		log.Printf("读取X.224失败: %v", err)
		return
	}
	log.Printf("[client->proxy] X.224 CR: TPKT=%d, bodyLen=%d", req.TPKTTotalLength, len(body))

	//----------------------------------
	// 第二步：发送 X.224 CC(选择TLS)
	//----------------------------------
	if err := writeX224ConnectionConfirm(c); err != nil {
		log.Printf("发送X.224 Confirm失败: %v", err)
		return
	}
	log.Printf("[proxy->client] X.224 CC 已发送，开始TLS握手...")

	//----------------------------------
	// 第三步：把连接升级为TLS (服务器模式)
	//         这样就能在tls.Config的GetConfigForClient回调里拿到SNI
	//----------------------------------
	tlsServerConn := tls.Server(c, tlsConfigForClients)
	if err := tlsServerConn.Handshake(); err != nil {
		log.Printf("与客户端的TLS握手失败: %v", err)
		return
	}
	log.Printf("客户端TLS握手完成，协商版本=%x, cipher=%x", tlsServerConn.ConnectionState().Version, tlsServerConn.ConnectionState().CipherSuite)

	// 从握手信息里拿到SNI
	sni := tlsServerConn.ConnectionState().ServerName
	if sni == "" {
		log.Printf("警告：客户端TLS握手没有携带SNI，无法根据域名路由！")
		// 这里可以考虑直接关闭，也可选择一个默认后端
		return
	}
	sni = strings.ToLower(sni)
	log.Printf("客户端 SNI: %s", sni)
	// 根据SNI在配置表中查找后端地址
	globalBackendConfig.RLock()
	backendAddr, ok := globalBackendConfig.DomainToAddr[sni]
	globalBackendConfig.RUnlock()
	if !ok {
		log.Printf("没有匹配到后端配置，SNI=%s", sni)
		return
	}
	log.Printf("准备转发到后端: %s", backendAddr)

	//----------------------------------
	// 第四步：与后端建立连接，完成 X.224 + TLS(客户端模式)，然后开始转发
	//----------------------------------
	backendConn, err := net.Dial("tcp", backendAddr)
	if err != nil {
		log.Printf("连接后端(%s)失败: %v", backendAddr, err)
		return
	}
	log.Printf("[proxy->backend] 已连接后端 %s", backendAddr)

	// 先发起 X.224 CR（简化）
	if err := sendX224CR(backendConn); err != nil {
		log.Printf("向后端发送X.224 CR失败: %v", err)
		backendConn.Close()
		return
	}

	// 读取后端的 X.224 CC
	if err := readX224CC(backendConn); err != nil {
		log.Printf("读取后端X.224 CC失败: %v", err)
		backendConn.Close()
		return
	}
	log.Printf("[backend->proxy] X.224 CC 收到，开始与后端TLS握手...")

	// 升级TLS（客户端模式）
	tlsClientConn := tls.Client(backendConn, tlsConfigForBackends)
	if err := tlsClientConn.Handshake(); err != nil {
		log.Printf("与后端TLS握手失败: %v", err)
		tlsClientConn.Close()
		return
	}
	log.Printf("后端TLS握手成功，version=%x cipher=%x", tlsClientConn.ConnectionState().Version, tlsClientConn.ConnectionState().CipherSuite)

	//-------------------------
	// 第五步：开始双向转发
	//-------------------------
	// 客户端<->代理(tlsServerConn) <-> 代理(tlsClientConn)<-> 后端
	// 这里不再解析RDP数据，只做TCP流量中继

	go func() {
		_, err := io.Copy(tlsServerConn, tlsClientConn)
		if err != nil {
			log.Printf("数据转发错误1: %v", err)
		}
		tlsServerConn.Close()
	}()

	_, err = io.Copy(tlsClientConn, tlsServerConn)
	if err != nil {
		log.Printf("数据转发错误2: %v", err)
	}
	tlsClientConn.Close()

}

//------------------------------------------------------
// 4.1) 与后端的 X.224 CR/CC 处理：简化示例
//------------------------------------------------------

func sendX224CR(conn net.Conn) error {
	// 仅做最简示例，TPKT+X.224 CR + RDP_NEG_REQ(选TLS=0x01)
	// 实际请根据 MS-RDPBCGR 做更完整的拼装
	// X.224 Connection Request
	x224CR := []byte{
		// TPKT Header (4 bytes)
		0x03, 0x00,
		0x00, 0x13, // Length: 19 bytes

		// X.224 Connection Request (7 bytes)
		0x0E,       // Length
		0xE0,       // CR - Connection Request
		0x00, 0x00, // DST-REF
		0x00, 0x00, // SRC-REF
		0x00, // CLASS

		// RDP Negotiation Request (8 bytes)
		0x01,       // Type: RDP_NEG_REQ
		0x00,       // Flags
		0x08, 0x00, // Length (8 bytes)
		0x01, 0x00, 0x00, 0x00, // requestedProtocols: TLS (0x00000001)
	}
	_, err := conn.Write(x224CR)
	return err
}

func readX224CC(conn net.Conn) error {
	// 简单读4字节TPKT头
	hdr := make([]byte, 4)
	//println("hdr", hdr)
	if _, err := io.ReadFull(conn, hdr); err != nil {
		return err
	}
	//println("hdr,ok")
	if hdr[0] != 3 {
		return fmt.Errorf("后端返回TPKT version不是3")
	}
	totalLen := int(hdr[2])<<8 | int(hdr[3])
	if totalLen < 4 {
		return fmt.Errorf("后端返回totalLen无效")
	}
	//println("totalLen", totalLen)
	rest := make([]byte, totalLen-4)
	if _, err := io.ReadFull(conn, rest); err != nil {
		return err
	}
	// 不做详细校验，认为成功
	return nil
}

//------------------------------------------------------
// 5) main入口：初始化TLS配置、启动HTTP API和RDP代理
//------------------------------------------------------

func main() {
	//----------------------------------------
	// 5.1) 加载或生成代理端自身的证书(用于给客户端握手)
	//----------------------------------------
	cert, err := tls.LoadX509KeyPair("server.crt", "server.key")
	if err != nil {
		log.Fatalf("加载代理证书出错: %v", err)
	}
	tlsConfigForClients = &tls.Config{
		Certificates: []tls.Certificate{cert},
		// 在多域名场景下，可以使用 GetCertificate 回调根据 SNI 动态加载不同证书
		// 或者配置Certificates=[]tls.Certificate{...}里多个证书
		// 也可以在这里加 VerifyPeerCertificate 等
		MinVersion: tls.VersionTLS12,
		MaxVersion: tls.VersionTLS13,
	}

	//----------------------------------------
	// 5.2) 代理端作为RDP客户端拨号后端的TLS配置
	//----------------------------------------
	tlsConfigForBackends = &tls.Config{
		InsecureSkipVerify: true, // 为了示例方便，跳过后端证书校验
		//MinVersion:         tls.VersionTLS12,
		//MaxVersion:         tls.VersionTLS13,
	}

	//----------------------------------------
	// 5.3) 启动HTTP API(管理域名->后端表)
	//----------------------------------------
	go startHTTPAPI(":8080")
	//----------------------------------------
	// 5.4) 启动RDP代理服务器(监听3389)
	//----------------------------------------
	startRDPProxy(":3389")
}
