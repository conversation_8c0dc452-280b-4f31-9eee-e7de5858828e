@echo off
echo 构建RDP SNI代理服务器...

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go语言
    pause
    exit /b 1
)

REM 下载依赖
echo 下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖失败
    pause
    exit /b 1
)

REM 编译程序
echo 编译程序...
go build -o rdp-proxy.exe cmd/main.go
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo 构建成功！
echo 可执行文件: rdp-proxy.exe
echo.
echo 使用方法:
echo 1. 将TLS证书文件放置在certs目录下
echo 2. 运行: rdp-proxy.exe
echo.
pause
