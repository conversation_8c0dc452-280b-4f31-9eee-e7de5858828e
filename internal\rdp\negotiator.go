package rdp

import (
	"fmt"
	"log"
	"net"
	"time"
)

// NegotiationState 协商状态
type NegotiationState int

const (
	StateInit NegotiationState = iota
	StateX224Connected
	StateMCSConnected
	StateGCCNegotiated
	StateTLSReady
	StateCompleted
)

// Negotiator RDP协商器
type Negotiator struct {
	clientConn       net.Conn
	backendConn      net.Conn
	state            NegotiationState
	selectedProtocol uint32
}

// NewNegotiator 创建新的协商器
func NewNegotiator(clientConn net.Conn) *Negotiator {
	return &Negotiator{
		clientConn:       clientConn,
		state:            StateInit,
		selectedProtocol: PROTOCOL_SSL, // 默认使用SSL
	}
}

// HandleClientNegotiation 处理客户端协商
func (n *Negotiator) HandleClientNegotiation() error {
	log.Printf("开始RDP协商处理")

	// 阶段1: 处理X.224连接请求
	if err := n.handleX224ConnectionRequest(); err != nil {
		return fmt.Errorf("X.224协商失败: %v", err)
	}

	// 阶段2: 处理MCS连接
	if err := n.handleMCSConnection(); err != nil {
		return fmt.Errorf("MCS协商失败: %v", err)
	}

	// 阶段3: 处理GCC协商
	if err := n.handleGCCNegotiation(); err != nil {
		return fmt.Errorf("GCC协商失败: %v", err)
	}

	log.Printf("RDP协商完成，准备TLS握手")
	n.state = StateTLSReady
	return nil
}

// handleX224ConnectionRequest 处理X.224连接请求
func (n *Negotiator) handleX224ConnectionRequest() error {
	log.Printf("等待X.224连接请求...")

	// 读取TPKT头
	tpktHeader := make([]byte, 4)
	if _, err := n.clientConn.Read(tpktHeader); err != nil {
		return fmt.Errorf("读取TPKT头失败: %v", err)
	}

	if tpktHeader[0] != 0x03 || tpktHeader[1] != 0x00 {
		return fmt.Errorf("无效的TPKT头: %02x %02x", tpktHeader[0], tpktHeader[1])
	}

	// 获取数据长度
	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])
	if dataLength < 4 {
		return fmt.Errorf("无效的TPKT长度: %d", dataLength)
	}

	// 读取X.224数据
	x224Data := make([]byte, dataLength-4)
	if _, err := n.clientConn.Read(x224Data); err != nil {
		return fmt.Errorf("读取X.224数据失败: %v", err)
	}

	// 解析X.224连接请求
	req, err := ParseX224ConnectionRequest(x224Data)
	if err != nil {
		return fmt.Errorf("解析X.224连接请求失败: %v", err)
	}

	log.Printf("收到X.224连接请求: SrcRef=%d, DstRef=%d", req.SrcRef, req.DstRef)

	// 确定协议
	if req.RDPNegData != nil {
		log.Printf("客户端请求协议: 0x%08x", req.RDPNegData.RequestedProtocols)
		if req.RDPNegData.RequestedProtocols&PROTOCOL_SSL != 0 {
			n.selectedProtocol = PROTOCOL_SSL
		} else if req.RDPNegData.RequestedProtocols&PROTOCOL_HYBRID != 0 {
			n.selectedProtocol = PROTOCOL_HYBRID
		} else {
			n.selectedProtocol = PROTOCOL_RDP
		}
	}

	// 发送X.224连接确认
	cc := BuildX224ConnectionConfirm(req.SrcRef, req.DstRef, n.selectedProtocol)
	tpktCC := WrapTPKT(cc)

	if _, err := n.clientConn.Write(tpktCC); err != nil {
		return fmt.Errorf("发送X.224连接确认失败: %v", err)
	}

	log.Printf("发送X.224连接确认，选择协议: 0x%08x", n.selectedProtocol)
	n.state = StateX224Connected
	return nil
}

// handleMCSConnection 处理MCS连接
func (n *Negotiator) handleMCSConnection() error {
	log.Printf("等待MCS连接初始化...")

	// 读取TPKT头
	tpktHeader := make([]byte, 4)
	if _, err := n.clientConn.Read(tpktHeader); err != nil {
		return fmt.Errorf("读取MCS TPKT头失败: %v", err)
	}

	// 获取数据长度
	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])

	// 读取MCS数据
	mcsData := make([]byte, dataLength-4)
	if _, err := n.clientConn.Read(mcsData); err != nil {
		return fmt.Errorf("读取MCS数据失败: %v", err)
	}

	// 解析MCS Connect-Initial
	_, err := ParseMCSConnectInitial(mcsData)
	if err != nil {
		return fmt.Errorf("解析MCS Connect-Initial失败: %v", err)
	}

	log.Printf("收到MCS Connect-Initial")

	// 发送MCS Connect-Response
	mcr := BuildMCSConnectResponse()
	tpktMCR := WrapTPKT(mcr)

	if _, err := n.clientConn.Write(tpktMCR); err != nil {
		return fmt.Errorf("发送MCS Connect-Response失败: %v", err)
	}

	log.Printf("发送MCS Connect-Response")
	n.state = StateMCSConnected

	// 处理后续的MCS消息（Erect Domain Request, Attach User Request等）
	if err := n.handleMCSSubsequentMessages(); err != nil {
		return fmt.Errorf("处理MCS后续消息失败: %v", err)
	}

	return nil
}

// handleMCSSubsequentMessages 处理MCS后续消息
func (n *Negotiator) handleMCSSubsequentMessages() error {
	// 处理Erect Domain Request
	if err := n.handleErectDomainRequest(); err != nil {
		return err
	}

	// 处理Attach User Request
	if err := n.handleAttachUserRequest(); err != nil {
		return err
	}

	// 处理Channel Join Request
	if err := n.handleChannelJoinRequests(); err != nil {
		return err
	}

	return nil
}

// handleErectDomainRequest 处理Erect Domain Request
func (n *Negotiator) handleErectDomainRequest() error {
	// 设置较短的读取超时，因为可能没有更多MCS消息
	// 读取Erect Domain Request
	tpktHeader := make([]byte, 4)
	n.clientConn.SetReadDeadline(time.Now().Add(2 * time.Second))
	defer n.clientConn.SetReadDeadline(time.Time{})

	_, err := n.clientConn.Read(tpktHeader)
	if err != nil {
		// 可能客户端没有发送Erect Domain Request，这是正常的
		log.Printf("没有收到Erect Domain Request，继续处理: %v", err)
		return nil
	}

	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])
	if dataLength > 4 {
		data := make([]byte, dataLength-4)
		if _, err := n.clientConn.Read(data); err != nil {
			log.Printf("读取Erect Domain Request数据失败: %v", err)
			return nil // 不返回错误，继续处理
		}
		log.Printf("收到Erect Domain Request")
	}

	return nil
}

// handleAttachUserRequest 处理Attach User Request
func (n *Negotiator) handleAttachUserRequest() error {
	// 设置读取超时
	tpktHeader := make([]byte, 4)
	n.clientConn.SetReadDeadline(time.Now().Add(2 * time.Second))
	defer n.clientConn.SetReadDeadline(time.Time{})

	_, err := n.clientConn.Read(tpktHeader)
	if err != nil {
		log.Printf("没有收到Attach User Request，继续处理: %v", err)
		return nil
	}

	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])
	if dataLength > 4 {
		data := make([]byte, dataLength-4)
		if _, err := n.clientConn.Read(data); err != nil {
			log.Printf("读取Attach User Request数据失败: %v", err)
			return nil
		}
		log.Printf("收到Attach User Request")

		// 发送Attach User Confirm
		attachUserConfirm := []byte{
			0x2e,       // Attach User Confirm
			0x00,       // Result: rt-successful
			0x00, 0x07, // User ID (1007)
		}

		tpktAUC := WrapTPKT(attachUserConfirm)
		if _, err := n.clientConn.Write(tpktAUC); err != nil {
			log.Printf("发送Attach User Confirm失败: %v", err)
			return nil
		}

		log.Printf("发送Attach User Confirm")
	}

	return nil
}

// handleChannelJoinRequests 处理Channel Join Requests
func (n *Negotiator) handleChannelJoinRequests() error {
	// 通常会有多个Channel Join Request，但我们简化处理
	// 设置较短的超时，如果没有更多消息就继续
	for i := 0; i < 10; i++ { // 最多处理10个
		tpktHeader := make([]byte, 4)
		n.clientConn.SetReadDeadline(time.Now().Add(1 * time.Second))

		_, err := n.clientConn.Read(tpktHeader)
		if err != nil {
			// 没有更多的Channel Join Request，这是正常的
			log.Printf("没有更多MCS消息，RDP协商可能完成: %v", err)
			break
		}

		n.clientConn.SetReadDeadline(time.Time{})

		dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])
		if dataLength <= 4 {
			continue
		}

		data := make([]byte, dataLength-4)
		if _, err := n.clientConn.Read(data); err != nil {
			log.Printf("读取MCS消息数据失败: %v", err)
			break
		}

		if len(data) >= 3 && data[0] == 0x38 { // Channel Join Request
			channelId := uint16(data[1])<<8 | uint16(data[2])
			log.Printf("收到Channel Join Request for channel %d", channelId)

			// 发送Channel Join Confirm
			channelJoinConfirm := []byte{
				0x3e,       // Channel Join Confirm
				0x00,       // Result: rt-successful
				0x00, 0x07, // User ID
				data[1], data[2], // Channel ID (echo back)
			}

			tpktCJC := WrapTPKT(channelJoinConfirm)
			if _, err := n.clientConn.Write(tpktCJC); err != nil {
				log.Printf("发送Channel Join Confirm失败: %v", err)
				break
			}

			log.Printf("发送Channel Join Confirm for channel %d", channelId)
		} else {
			// 不是Channel Join Request，可能是其他消息或TLS握手开始
			log.Printf("收到非Channel Join Request消息: %02x，可能是TLS握手开始", data[0])
			// 将这个数据放回连接中供后续处理
			// 这里我们简化处理，直接结束MCS阶段
			break
		}
	}

	n.clientConn.SetReadDeadline(time.Time{})
	return nil
}

// handleGCCNegotiation 处理GCC协商
func (n *Negotiator) handleGCCNegotiation() error {
	log.Printf("GCC协商已在MCS阶段完成")
	n.state = StateGCCNegotiated
	return nil
}

// GetState 获取当前状态
func (n *Negotiator) GetState() NegotiationState {
	return n.state
}

// GetSelectedProtocol 获取选择的协议
func (n *Negotiator) GetSelectedProtocol() uint32 {
	return n.selectedProtocol
}

// SetBackendConn 设置后端连接
func (n *Negotiator) SetBackendConn(conn net.Conn) {
	n.backendConn = conn
}
