package rdp

import (
	"fmt"
	"log"
	"net"
)

// NegotiationState 协商状态
type NegotiationState int

const (
	StateInit NegotiationState = iota
	StateX224Connected
	StateMCSConnected
	StateGCCNegotiated
	StateTLSReady
	StateCompleted
)

// Negotiator RDP协商器
type Negotiator struct {
	clientConn       net.Conn
	backendConn      net.Conn
	state            NegotiationState
	selectedProtocol uint32
}

// NewNegotiator 创建新的协商器
func NewNegotiator(clientConn net.Conn) *Negotiator {
	return &Negotiator{
		clientConn:       clientConn,
		state:            StateInit,
		selectedProtocol: PROTOCOL_SSL, // 默认使用SSL
	}
}

// HandleClientNegotiation 处理客户端协商
func (n *Negotiator) HandleClientNegotiation() error {
	log.Printf("开始RDP协商处理")

	// 阶段1: 处理X.224连接请求
	if err := n.handleX224ConnectionRequest(); err != nil {
		return fmt.Errorf("X.224协商失败: %v", err)
	}

	// 阶段2: 处理MCS连接
	if err := n.handleMCSConnection(); err != nil {
		return fmt.Errorf("MCS协商失败: %v", err)
	}

	// 阶段3: 处理GCC协商
	if err := n.handleGCCNegotiation(); err != nil {
		return fmt.Errorf("GCC协商失败: %v", err)
	}

	log.Printf("RDP协商完成，准备TLS握手")
	n.state = StateTLSReady
	return nil
}

// handleX224ConnectionRequest 处理X.224连接请求
func (n *Negotiator) handleX224ConnectionRequest() error {
	log.Printf("等待X.224连接请求...")

	// 读取TPKT头
	tpktHeader := make([]byte, 4)
	if _, err := n.clientConn.Read(tpktHeader); err != nil {
		return fmt.Errorf("读取TPKT头失败: %v", err)
	}

	if tpktHeader[0] != 0x03 || tpktHeader[1] != 0x00 {
		return fmt.Errorf("无效的TPKT头: %02x %02x", tpktHeader[0], tpktHeader[1])
	}

	// 获取数据长度
	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])
	if dataLength < 4 {
		return fmt.Errorf("无效的TPKT长度: %d", dataLength)
	}

	// 读取X.224数据
	x224Data := make([]byte, dataLength-4)
	if _, err := n.clientConn.Read(x224Data); err != nil {
		return fmt.Errorf("读取X.224数据失败: %v", err)
	}

	// 解析X.224连接请求
	req, err := ParseX224ConnectionRequest(x224Data)
	if err != nil {
		return fmt.Errorf("解析X.224连接请求失败: %v", err)
	}

	log.Printf("收到X.224连接请求: SrcRef=%d, DstRef=%d", req.SrcRef, req.DstRef)

	// 确定协议
	if req.RDPNegData != nil {
		log.Printf("客户端请求协议: 0x%08x", req.RDPNegData.RequestedProtocols)
		if req.RDPNegData.RequestedProtocols&PROTOCOL_SSL != 0 {
			n.selectedProtocol = PROTOCOL_SSL
		} else if req.RDPNegData.RequestedProtocols&PROTOCOL_HYBRID != 0 {
			n.selectedProtocol = PROTOCOL_HYBRID
		} else {
			n.selectedProtocol = PROTOCOL_RDP
		}
	}

	// 发送X.224连接确认
	cc := BuildX224ConnectionConfirm(req.SrcRef, req.DstRef, n.selectedProtocol)
	tpktCC := WrapTPKT(cc)

	if _, err := n.clientConn.Write(tpktCC); err != nil {
		return fmt.Errorf("发送X.224连接确认失败: %v", err)
	}

	log.Printf("发送X.224连接确认，选择协议: 0x%08x", n.selectedProtocol)
	n.state = StateX224Connected
	return nil
}

// handleMCSConnection 处理MCS连接
func (n *Negotiator) handleMCSConnection() error {
	log.Printf("等待MCS连接初始化...")

	// 读取TPKT头
	tpktHeader := make([]byte, 4)
	if _, err := n.clientConn.Read(tpktHeader); err != nil {
		return fmt.Errorf("读取MCS TPKT头失败: %v", err)
	}

	// 获取数据长度
	dataLength := int(tpktHeader[2])<<8 | int(tpktHeader[3])

	// 读取MCS数据
	mcsData := make([]byte, dataLength-4)
	if _, err := n.clientConn.Read(mcsData); err != nil {
		return fmt.Errorf("读取MCS数据失败: %v", err)
	}

	// 解析MCS Connect-Initial
	_, err := ParseMCSConnectInitial(mcsData)
	if err != nil {
		return fmt.Errorf("解析MCS Connect-Initial失败: %v", err)
	}

	log.Printf("收到MCS Connect-Initial")

	// 发送MCS Connect-Response
	mcr := BuildMCSConnectResponse()
	tpktMCR := WrapTPKT(mcr)

	if _, err := n.clientConn.Write(tpktMCR); err != nil {
		return fmt.Errorf("发送MCS Connect-Response失败: %v", err)
	}

	log.Printf("发送MCS Connect-Response")
	n.state = StateMCSConnected

	// 简化处理：发送MCS Connect-Response后，客户端应该开始TLS握手
	// 不再等待Erect Domain Request等消息，因为这些在TLS握手后处理
	log.Printf("MCS协商完成，等待TLS握手")

	return nil
}

// handleGCCNegotiation 处理GCC协商
func (n *Negotiator) handleGCCNegotiation() error {
	log.Printf("GCC协商已在MCS阶段完成")
	n.state = StateGCCNegotiated
	return nil
}

// GetState 获取当前状态
func (n *Negotiator) GetState() NegotiationState {
	return n.state
}

// GetSelectedProtocol 获取选择的协议
func (n *Negotiator) GetSelectedProtocol() uint32 {
	return n.selectedProtocol
}

// SetBackendConn 设置后端连接
func (n *Negotiator) SetBackendConn(conn net.Conn) {
	n.backendConn = conn
}
