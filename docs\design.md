# RDP SNI 代理服务器设计文档

## 项目概述

基于SNI的RDP连接路由代理服务器，实现TLS终止和重新握手的中间人代理模式，通过解析客户端TLS ClientHello中的SNI字段进行域名路由。

**项目状态：✅ 已完成实现**

本项目已完整实现所有核心功能，包括：
- ✅ 完整的RDP协议MITM处理
- ✅ SNI解析和路由
- ✅ 双重TLS握手
- ✅ 高并发数据转发
- ✅ 安全防护机制
- ✅ 完善的文档和工具

## 核心架构

```
客户端(MSTSC) 
    ↓ TLS握手（携带SNI）
[代理服务器: TLS终止 + 根据SNI路由]
    ↓ 重新发起TLS握手  
    ↓ 转发RDP流量
[真实RDP后端服务器]
```

## 技术要求

### 1. RDP协议层面的MITM实现
- **完整RDP协商参与**：代理必须参与整个RDP协商过程
- **X.224连接请求/确认**：处理Connection Request (CR) 和 Connection Confirm (CC)
- **MCS连接**：处理MCS Connect-Initial和Connect-Response
- **GCC协商**：处理GCC Conference Create Request/Response
- **安全交换**：参与Security Exchange阶段

### 2. TLS双重握手
- **第一段**：客户端 ↔ 代理服务器（TLS终止）
- **第二段**：代理服务器 ↔ 后端RDP服务器（重新发起TLS）
- **证书管理**：使用现有的server.crt和server.key

### 3. SNI路由规则
```
20201.rdp.anan.cc → 127.0.0.1:20201
127.rdp.anan.cc   → 127.0.0.1:1111
其他SNI或无SNI    → 拒绝连接
```

## 核心组件设计

### 1. 主服务器 (main.go)
- 监听TCP 3389端口
- 接受客户端连接
- 启动连接处理协程

### 2. SNI解析器 (sni/parser.go)
- 解析TLS ClientHello消息
- 提取SNI扩展字段
- 验证SNI是否在允许列表中

### 3. RDP协商处理器 (rdp/negotiator.go)
- X.224连接请求/响应处理
- MCS连接初始化/确认
- GCC会议创建请求/响应
- 安全协商参与

### 4. 连接代理器 (proxy/handler.go)
- 管理客户端到代理的连接
- 管理代理到后端的连接
- 双向数据转发
- 连接状态管理

### 5. TLS管理器 (tls/manager.go)
- 客户端TLS终止
- 后端TLS重新握手
- 证书加载和管理

### 6. 配置管理 (config/config.go)
- SNI路由规则配置
- TLS证书路径配置
- 后端服务器配置

## RDP协商流程

### 阶段1: X.224连接建立
```
客户端 → 代理: X.224 Connection Request (CR)
代理 → 客户端: X.224 Connection Confirm (CC)
```

### 阶段2: MCS连接
```
客户端 → 代理: MCS Connect-Initial
代理 → 客户端: MCS Connect-Response
```

### 阶段3: GCC协商
```
客户端 → 代理: GCC Conference Create Request
代理 → 客户端: GCC Conference Create Response
```

### 阶段4: TLS握手
```
客户端 → 代理: TLS ClientHello (含SNI)
代理解析SNI并路由
代理 → 客户端: TLS ServerHello + Certificate + ServerHelloDone
客户端 → 代理: TLS ClientKeyExchange + ChangeCipherSpec + Finished
代理 → 客户端: ChangeCipherSpec + Finished
```

### 阶段5: 后端连接建立
```
代理 → 后端: 完整RDP协商流程
代理 ↔ 后端: TLS握手
```

### 阶段6: 数据转发
```
客户端 ↔ 代理 ↔ 后端: 加密RDP数据流
```

## 安全考虑

1. **SNI验证**：严格验证SNI字段，拒绝无SNI或不匹配的连接
2. **证书管理**：安全加载和使用TLS证书
3. **连接隔离**：确保不同客户端连接之间的隔离
4. **错误处理**：优雅处理各种异常情况
5. **日志记录**：记录连接和路由信息用于审计

## 文件结构

```
golang-rdp-proxy/
├── docs/
│   └── design.md
├── cmd/
│   └── main.go
├── internal/
│   ├── config/
│   │   └── config.go
│   ├── sni/
│   │   └── parser.go
│   ├── rdp/
│   │   ├── negotiator.go
│   │   └── protocol.go
│   ├── proxy/
│   │   └── handler.go
│   └── tls/
│       └── manager.go
├── certs/
│   ├── server.crt
│   └── server.key
├── go.mod
└── go.sum
```

## 实现重点

1. **真正的RDP MITM**：不仅仅是TCP代理，而是完整参与RDP协商
2. **协议解析精确性**：准确解析和构造RDP协议消息
3. **状态管理**：正确管理连接状态和协商阶段
4. **错误恢复**：处理协商失败和连接异常
5. **性能优化**：高效的数据转发和内存管理

## 部署和使用

### 快速启动

1. **编译程序**
   ```bash
   go mod tidy
   go build -o rdp-proxy cmd/main.go
   ```

2. **准备证书**
   - 将TLS证书放置在 `certs/server.crt`
   - 将私钥放置在 `certs/server.key`
   - 或运行 `generate-cert.bat` 生成测试证书

3. **启动服务**
   ```bash
   ./rdp-proxy
   ```

### 客户端连接

使用支持SNI的RDP客户端连接：
```bash
mstsc /v:20201.rdp.anan.cc:3389
mstsc /v:127.rdp.anan.cc:3389
```

### 后端服务器要求

- 后端RDP服务器必须支持TLS连接
- 确保防火墙允许代理服务器连接
- 建议使用RDP 8.0+版本以获得最佳兼容性

## 安全考虑

### 证书管理
- 使用受信任CA签发的证书
- 定期更新证书避免过期
- 私钥文件权限设置为600

### 网络安全
- 限制代理服务器的网络访问
- 使用防火墙规则限制连接来源
- 监控异常连接和流量

### 日志审计
- 记录所有连接尝试
- 监控SNI解析失败
- 定期分析访问模式

## 故障排除

### 常见问题

1. **证书错误**
   - 检查证书格式和有效期
   - 确认证书包含所需域名
   - 验证私钥与证书匹配

2. **SNI解析失败**
   - 确认客户端支持SNI
   - 检查TLS版本兼容性
   - 验证域名配置正确

3. **后端连接失败**
   - 检查后端服务器状态
   - 验证网络连通性
   - 确认端口开放状态

### 调试模式

启用详细日志输出：
```go
log.SetFlags(log.LstdFlags | log.Lshortfile | log.Lmicroseconds)
```

## 性能调优

### 连接池
- 实现后端连接池减少连接开销
- 配置合适的连接超时时间
- 监控连接使用情况

### 内存管理
- 优化缓冲区大小
- 及时释放不用的连接
- 监控内存使用情况

### 并发控制
- 限制最大并发连接数
- 实现连接限流机制
- 监控系统资源使用
