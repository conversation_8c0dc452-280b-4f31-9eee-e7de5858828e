package sni

import (
	"encoding/binary"
	"fmt"
	"io"
)

const (
	// TLS记录类型
	TLSRecordTypeHandshake = 0x16

	// TLS握手类型
	TLSHandshakeTypeClientHello = 0x01

	// TLS扩展类型
	TLSExtensionTypeSNI = 0x0000

	// SNI名称类型
	SNINameTypeHostName = 0x00
)

// TLSRecord TLS记录结构
type TLSRecord struct {
	Type    uint8
	Version uint16
	Length  uint16
	Data    []byte
}

// ClientHello 客户端Hello消息结构
type ClientHello struct {
	Version            uint16
	Random             [32]byte
	SessionIDLength    uint8
	SessionID          []byte
	CipherSuitesLength uint16
	CipherSuites       []uint16
	CompressionLength  uint8
	CompressionMethods []uint8
	ExtensionsLength   uint16
	Extensions         []Extension
}

// Extension TLS扩展结构
type Extension struct {
	Type   uint16
	Length uint16
	Data   []byte
}

// SNIExtension SNI扩展结构
type SNIExtension struct {
	ServerNameListLength uint16
	ServerNames          []ServerName
}

// ServerName 服务器名称结构
type ServerName struct {
	Type   uint8
	Length uint16
	Name   string
}

// ParseSNIFromTLS 从TLS数据中解析SNI
func ParseSNIFromTLS(data []byte) (string, error) {
	if len(data) < 5 {
		return "", fmt.Errorf("TLS record too short")
	}

	// 解析TLS记录头
	record := &TLSRecord{
		Type:    data[0],
		Version: binary.BigEndian.Uint16(data[1:3]),
		Length:  binary.BigEndian.Uint16(data[3:5]),
	}

	if record.Type != TLSRecordTypeHandshake {
		return "", fmt.Errorf("not a TLS handshake record")
	}

	if len(data) < int(5+record.Length) {
		return "", fmt.Errorf("incomplete TLS record")
	}

	record.Data = data[5 : 5+record.Length]

	// 解析握手消息
	if len(record.Data) < 4 {
		return "", fmt.Errorf("handshake message too short")
	}

	handshakeType := record.Data[0]
	if handshakeType != TLSHandshakeTypeClientHello {
		return "", fmt.Errorf("not a ClientHello message")
	}

	handshakeLength := uint32(record.Data[1])<<16 | uint32(record.Data[2])<<8 | uint32(record.Data[3])
	if len(record.Data) < int(4+handshakeLength) {
		return "", fmt.Errorf("incomplete ClientHello message")
	}

	// 解析ClientHello
	clientHello, err := parseClientHello(record.Data[4 : 4+handshakeLength])
	if err != nil {
		return "", fmt.Errorf("failed to parse ClientHello: %v", err)
	}

	// 查找SNI扩展
	for _, ext := range clientHello.Extensions {
		if ext.Type == TLSExtensionTypeSNI {
			sni, err := parseSNIExtension(ext.Data)
			if err != nil {
				return "", fmt.Errorf("failed to parse SNI extension: %v", err)
			}
			return sni, nil
		}
	}

	return "", fmt.Errorf("SNI extension not found")
}

// parseClientHello 解析ClientHello消息
func parseClientHello(data []byte) (*ClientHello, error) {
	if len(data) < 38 {
		return nil, fmt.Errorf("ClientHello too short")
	}

	ch := &ClientHello{}
	offset := 0

	// 版本号
	ch.Version = binary.BigEndian.Uint16(data[offset : offset+2])
	offset += 2

	// 随机数
	copy(ch.Random[:], data[offset:offset+32])
	offset += 32

	// 会话ID
	ch.SessionIDLength = data[offset]
	offset++
	if len(data) < offset+int(ch.SessionIDLength) {
		return nil, fmt.Errorf("invalid session ID length")
	}
	ch.SessionID = make([]byte, ch.SessionIDLength)
	copy(ch.SessionID, data[offset:offset+int(ch.SessionIDLength)])
	offset += int(ch.SessionIDLength)

	// 密码套件
	if len(data) < offset+2 {
		return nil, fmt.Errorf("missing cipher suites length")
	}
	ch.CipherSuitesLength = binary.BigEndian.Uint16(data[offset : offset+2])
	offset += 2
	if len(data) < offset+int(ch.CipherSuitesLength) {
		return nil, fmt.Errorf("invalid cipher suites length")
	}
	cipherSuitesCount := int(ch.CipherSuitesLength) / 2
	ch.CipherSuites = make([]uint16, cipherSuitesCount)
	for i := 0; i < cipherSuitesCount; i++ {
		ch.CipherSuites[i] = binary.BigEndian.Uint16(data[offset : offset+2])
		offset += 2
	}

	// 压缩方法
	if len(data) < offset+1 {
		return nil, fmt.Errorf("missing compression methods length")
	}
	ch.CompressionLength = data[offset]
	offset++
	if len(data) < offset+int(ch.CompressionLength) {
		return nil, fmt.Errorf("invalid compression methods length")
	}
	ch.CompressionMethods = make([]byte, ch.CompressionLength)
	copy(ch.CompressionMethods, data[offset:offset+int(ch.CompressionLength)])
	offset += int(ch.CompressionLength)

	// 扩展
	if len(data) <= offset {
		return ch, nil // 没有扩展
	}

	if len(data) < offset+2 {
		return nil, fmt.Errorf("missing extensions length")
	}
	ch.ExtensionsLength = binary.BigEndian.Uint16(data[offset : offset+2])
	offset += 2

	if len(data) < offset+int(ch.ExtensionsLength) {
		return nil, fmt.Errorf("invalid extensions length")
	}

	extensionsData := data[offset : offset+int(ch.ExtensionsLength)]
	extensions, err := parseExtensions(extensionsData)
	if err != nil {
		return nil, fmt.Errorf("failed to parse extensions: %v", err)
	}
	ch.Extensions = extensions

	return ch, nil
}

// parseExtensions 解析扩展
func parseExtensions(data []byte) ([]Extension, error) {
	var extensions []Extension
	offset := 0

	for offset < len(data) {
		if len(data) < offset+4 {
			return nil, fmt.Errorf("incomplete extension header")
		}

		ext := Extension{
			Type:   binary.BigEndian.Uint16(data[offset : offset+2]),
			Length: binary.BigEndian.Uint16(data[offset+2 : offset+4]),
		}
		offset += 4

		if len(data) < offset+int(ext.Length) {
			return nil, fmt.Errorf("incomplete extension data")
		}

		ext.Data = make([]byte, ext.Length)
		copy(ext.Data, data[offset:offset+int(ext.Length)])
		offset += int(ext.Length)

		extensions = append(extensions, ext)
	}

	return extensions, nil
}

// parseSNIExtension 解析SNI扩展
func parseSNIExtension(data []byte) (string, error) {
	if len(data) < 2 {
		return "", fmt.Errorf("SNI extension too short")
	}

	listLength := binary.BigEndian.Uint16(data[0:2])
	if len(data) < int(2+listLength) {
		return "", fmt.Errorf("invalid SNI list length")
	}

	offset := 2
	for offset < len(data) {
		if len(data) < offset+3 {
			return "", fmt.Errorf("incomplete SNI entry")
		}

		nameType := data[offset]
		nameLength := binary.BigEndian.Uint16(data[offset+1 : offset+3])
		offset += 3

		if len(data) < offset+int(nameLength) {
			return "", fmt.Errorf("incomplete SNI name")
		}

		if nameType == SNINameTypeHostName {
			return string(data[offset : offset+int(nameLength)]), nil
		}

		offset += int(nameLength)
	}

	return "", fmt.Errorf("hostname not found in SNI extension")
}

// PeekSNI 从连接中预读SNI信息
func PeekSNI(conn io.Reader) (string, []byte, error) {
	// 读取足够的数据来解析TLS记录头
	buffer := make([]byte, 4096)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", nil, fmt.Errorf("failed to read from connection: %v", err)
	}

	if n < 5 {
		return "", buffer[:n], fmt.Errorf("insufficient data for TLS record")
	}

	// 解析SNI
	sni, err := ParseSNIFromTLS(buffer[:n])
	if err != nil {
		return "", buffer[:n], err
	}

	return sni, buffer[:n], nil
}
