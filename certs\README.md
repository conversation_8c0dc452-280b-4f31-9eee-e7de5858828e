# TLS证书目录

此目录包含RDP代理服务器使用的TLS证书文件。

## 文件说明

- `server.crt` - 服务器证书文件
- `server.key` - 服务器私钥文件

## 证书要求

1. 证书必须包含所有需要支持的SNI域名
2. 建议使用通配符证书或SAN证书支持多个域名
3. 证书格式为PEM格式

## 生成自签名证书（仅用于测试）

```bash
# 生成私钥
openssl genrsa -out server.key 2048

# 生成证书签名请求
openssl req -new -key server.key -out server.csr

# 生成自签名证书
openssl x509 -req -days 365 -in server.csr -signkey server.key -out server.crt

# 或者一步生成自签名证书
openssl req -x509 -newkey rsa:2048 -keyout server.key -out server.crt -days 365 -nodes \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=Test/CN=*.rdp.anan.cc"
```

## 生产环境证书

在生产环境中，建议使用由受信任的CA签发的证书，如：
- Let's Encrypt
- DigiCert
- GlobalSign
- 其他商业CA

## 证书配置

证书路径在 `internal/config/config.go` 中配置：
```go
CertFile: "certs/server.crt",
KeyFile:  "certs/server.key",
```

## 安全注意事项

1. 私钥文件权限应设置为600（仅所有者可读写）
2. 定期更新证书，避免过期
3. 使用强加密算法和足够长的密钥
4. 在生产环境中不要使用自签名证书
