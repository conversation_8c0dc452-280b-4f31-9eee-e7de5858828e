package main

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"golang-rdp-proxy/internal/config"
	"golang-rdp-proxy/internal/proxy"
	"golang-rdp-proxy/internal/tls"
)

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Printf("启动RDP SNI代理服务器...")

	// 加载配置
	cfg := config.NewConfig()
	cfg.LogConfig()

	// 创建TLS管理器
	tlsManager, err := tls.NewManager(cfg.CertFile, cfg.KeyFile)
	if err != nil {
		log.Fatalf("创建TLS管理器失败: %v", err)
	}
	log.Printf("TLS证书加载成功")

	// 创建代理处理器
	handler := proxy.NewHandler(cfg, tlsManager)

	// 创建统计管理器
	statsManager := proxy.NewStatsManager()

	// 启动统计日志协程
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				statsManager.LogStats()
			}
		}
	}()

	// 监听端口
	listenAddr := fmt.Sprintf("%s:%d", cfg.ListenAddr, cfg.ListenPort)
	listener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		log.Fatalf("监听端口失败: %v", err)
	}
	defer listener.Close()

	log.Printf("RDP代理服务器启动成功，监听地址: %s", listenAddr)
	log.Printf("支持的SNI路由:")
	for sni, backend := range cfg.SNIRoutes {
		log.Printf("  %s -> %s", sni, backend)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动服务器协程
	go func() {
		for {
			conn, err := listener.Accept()
			if err != nil {
				log.Printf("接受连接失败: %v", err)
				continue
			}

			// 为每个连接启动处理协程
			go func(clientConn net.Conn) {
				clientAddr := clientConn.RemoteAddr().String()
				log.Printf("接受新连接: %s", clientAddr)
				
				// 处理连接
				handler.HandleConnection(clientConn)
				
				log.Printf("连接处理完成: %s", clientAddr)
			}(conn)
		}
	}()

	// 等待退出信号
	sig := <-sigChan
	log.Printf("收到信号 %v，正在关闭服务器...", sig)

	// 优雅关闭
	log.Printf("RDP代理服务器已关闭")
}

// 版本信息
const (
	Version   = "1.0.0"
	BuildTime = "2024-01-01"
	GitCommit = "unknown"
)

func init() {
	log.Printf("RDP SNI代理服务器")
	log.Printf("版本: %s", Version)
	log.Printf("构建时间: %s", BuildTime)
	log.Printf("Git提交: %s", GitCommit)
	log.Printf("Go版本: %s", "1.21+")
}
